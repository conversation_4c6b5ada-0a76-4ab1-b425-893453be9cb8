# Professional Download Manager

مدير تحميلات احترافي بلغة Python باستخدام PySide6، مصمم ليكون بديلاً قوياً لـ Internet Download Manager (IDM).

## الميزات الرئيسية

### 🚀 التحميل المتقدم
- **تحميل متوازي عالي السرعة** باستخدام aiohttp و asyncio
- **دعم الإيقاف والاستئناف** (Pause/Resume) مع Range headers
- **تحميل متعدد الاتصالات** لتسريع التحميل
- **كشف تلقائي لنوع الملف** من Content-Type أو امتداد الملف
- **فتح تلقائي للملفات** بعد اكتمال التحميل

### 📊 التحليل والإحصائيات
- **رسوم بيانية للسرعة اللحظية** باستخدام pyqtgraph
- **إحصائيات يومية وشهرية** للتحميلات والسرعة
- **تحليل أنواع الملفات** مع رسوم بيانية دائرية وشريطية
- **تتبع معدل النجاح** والأداء العام

### 🗄️ إدارة البيانات
- **قاعدة بيانات SQLite** لحفظ تاريخ التحميلات
- **بحث متقدم** في التحميلات المكتملة
- **تصدير البيانات** بصيغ JSON و CSV
- **تنظيف تلقائي** للبيانات القديمة

### 🌍 دعم متعدد اللغات
- **العربية** (افتراضي)
- **الإنجليزية**
- **الفرنسية**

### 🎨 واجهة مستخدم متقدمة
- **تصميم متجاوب** مع تبويبات متعددة
- **الوضع الليلي** (Dark Mode) مع ثيمات متنوعة
- **السحب والإفلات** للروابط
- **أيقونة النظام** مع إشعارات
- **قائمة انتظار** مع إدارة الأولويات

### 🔧 ميزات إضافية
- **مراقبة الحافظة** التلقائية للروابط
- **دعم البروكسي** و User Agent مخصص
- **إعدادات متقدمة** للشبكة والأداء
- **نظام إشعارات** شامل مع أصوات

## متطلبات النظام

- **نظام التشغيل:** Windows 10/11, Linux, macOS
- **Python:** 3.8 أو أحدث
- **الذاكرة:** 512 MB RAM كحد أدنى
- **المساحة:** 100 MB مساحة فارغة

## التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/professional-download-manager.git
cd professional-download-manager
```

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install PySide6>=6.5.0
pip install aiohttp>=3.8.0
pip install aiofiles>=23.0.0
pip install pyqtgraph>=0.13.0
pip install requests>=2.28.0
```

### 3. تشغيل التطبيق
```bash
python main.py
```

### اختبار التطبيق (إذا لم تكن المكتبات مثبتة)
```bash
python test_app.py
```

## بنية المشروع

```
professional-download-manager/
├── main.py                 # نقطة التشغيل الرئيسية
├── test_app.py            # اختبار مبسط للتطبيق
├── requirements.txt       # المكتبات المطلوبة
├── README.md             # هذا الملف
│
├── ui/                   # واجهات المستخدم
│   ├── __init__.py
│   ├── main_window.py           # النافذة الرئيسية
│   ├── download_widget.py       # واجهة التحميلات الحالية
│   ├── download_item_widget.py  # عنصر التحميل الفردي
│   ├── completed_widget.py      # واجهة التحميلات المكتملة
│   ├── analytics_widget.py      # واجهة التحليل البياني
│   ├── settings_widget.py       # واجهة الإعدادات
│   └── add_download_dialog.py   # حوار إضافة تحميل جديد
│
├── core/                 # المنطق الأساسي
│   ├── __init__.py
│   ├── download_manager.py      # مدير التحميلات الرئيسي
│   ├── database.py             # إدارة قاعدة البيانات
│   ├── file_utils.py           # أدوات تحليل الملفات
│   └── theme_manager.py        # مدير الثيمات والألوان
│
└── assets/               # الموارد
    ├── icons/           # الأيقونات
    ├── translations/    # ملفات الترجمة
    └── themes/         # ملفات الثيمات
```

## الاستخدام

### إضافة تحميل جديد
1. انقر على زر "إضافة تحميل" في الشريط العلوي
2. أدخل رابط الملف المراد تحميله
3. انقر على "تحليل" للحصول على معلومات الملف
4. اختر مجلد الحفظ والأولوية
5. انقر على "إضافة التحميل"

### إدارة التحميلات
- **إيقاف/استئناف:** انقر بزر الماوس الأيمن على التحميل
- **تغيير الأولوية:** استخدم زر "أولوية" في عنصر التحميل
- **إلغاء التحميل:** انقر على زر الإلغاء (X)

### عرض الإحصائيات
- انتقل إلى تبويب "الإحصائيات" لعرض الرسوم البيانية
- اختر الفترة الزمنية من القائمة المنسدلة
- استخدم زر "تصدير" لحفظ التحليلات

### تخصيص الإعدادات
- انتقل إلى تبويب "الإعدادات"
- اختر الثيم واللغة المفضلة
- اضبط إعدادات التحميل والشبكة
- انقر على "حفظ" لتطبيق التغييرات

## الميزات المتقدمة

### السحب والإفلات
- اسحب الروابط مباشرة إلى نافذة التطبيق
- سيتم فتح حوار إضافة التحميل تلقائياً

### مراقبة الحافظة
- فعّل "رصد الحافظة تلقائياً" في الإعدادات
- عند نسخ رابط، سيظهر حوار إضافة التحميل

### أيقونة النظام
- التطبيق يعمل في الخلفية عبر أيقونة النظام
- انقر نقراً مزدوجاً لإظهار/إخفاء النافذة
- انقر بزر الماوس الأيمن للوصول للقائمة

## استكشاف الأخطاء

### مشاكل شائعة

**1. خطأ في استيراد PySide6**
```bash
pip install --upgrade PySide6
```

**2. مشاكل في الاتصال**
- تحقق من إعدادات البروكسي
- جرب تغيير User Agent
- تأكد من الاتصال بالإنترنت

**3. بطء في التحميل**
- قلل عدد الاتصالات المتوازية
- تحقق من حد السرعة في الإعدادات
- جرب خادم DNS مختلف

**4. مشاكل في قاعدة البيانات**
- احذف ملف `downloads.db` لإعادة إنشاء قاعدة البيانات
- تأكد من صلاحيات الكتابة في مجلد التطبيق

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة (`git checkout -b feature/amazing-feature`)
3. تنفيذ التغييرات مع التعليقات
4. رفع التغييرات (`git commit -m 'Add amazing feature'`)
5. دفع الفرع (`git push origin feature/amazing-feature`)
6. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

- **الإبلاغ عن الأخطاء:** [GitHub Issues](https://github.com/your-repo/professional-download-manager/issues)
- **طلب ميزات جديدة:** [GitHub Discussions](https://github.com/your-repo/professional-download-manager/discussions)
- **التوثيق:** [Wiki](https://github.com/your-repo/professional-download-manager/wiki)

## الشكر والتقدير

- **PySide6** - واجهة المستخدم الرسومية
- **aiohttp** - HTTP client/server للتحميل المتوازي
- **pyqtgraph** - الرسوم البيانية العلمية
- **SQLite** - قاعدة البيانات المدمجة

---

**Professional Download Manager** - مدير تحميلات احترافي مفتوح المصدر 🚀