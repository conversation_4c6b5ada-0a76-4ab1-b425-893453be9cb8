#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات SQLite
Database Manager for Download History and Analytics
"""

import sqlite3
import json
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from contextlib import contextmanager

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = "downloads.db"):
        self.db_path = Path(db_path)
        self.init_database()
        
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # جدول التحميلات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS downloads (
                    id TEXT PRIMARY KEY,
                    url TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    save_path TEXT NOT NULL,
                    total_size INTEGER DEFAULT 0,
                    downloaded_size INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'waiting',
                    progress REAL DEFAULT 0.0,
                    speed REAL DEFAULT 0.0,
                    created_time REAL NOT NULL,
                    start_time REAL,
                    end_time REAL,
                    error_message TEXT,
                    file_type TEXT,
                    priority INTEGER DEFAULT 0,
                    resume_supported BOOLEAN DEFAULT 0
                )
            """)
            
            # جدول إحصائيات السرعة (لكل دقيقة)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS speed_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    download_id TEXT,
                    timestamp REAL NOT NULL,
                    speed REAL NOT NULL,
                    downloaded_bytes INTEGER NOT NULL,
                    FOREIGN KEY (download_id) REFERENCES downloads (id)
                )
            """)
            
            # جدول الإحصائيات اليومية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_stats (
                    date TEXT PRIMARY KEY,
                    total_downloads INTEGER DEFAULT 0,
                    completed_downloads INTEGER DEFAULT 0,
                    failed_downloads INTEGER DEFAULT 0,
                    total_bytes INTEGER DEFAULT 0,
                    avg_speed REAL DEFAULT 0.0,
                    max_speed REAL DEFAULT 0.0
                )
            """)
            
            # جدول الإحصائيات الشهرية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS monthly_stats (
                    month TEXT PRIMARY KEY,
                    total_downloads INTEGER DEFAULT 0,
                    completed_downloads INTEGER DEFAULT 0,
                    failed_downloads INTEGER DEFAULT 0,
                    total_bytes INTEGER DEFAULT 0,
                    avg_speed REAL DEFAULT 0.0,
                    max_speed REAL DEFAULT 0.0
                )
            """)
            
            # جدول أنواع الملفات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS file_type_stats (
                    file_type TEXT PRIMARY KEY,
                    count INTEGER DEFAULT 0,
                    total_bytes INTEGER DEFAULT 0,
                    avg_speed REAL DEFAULT 0.0
                )
            """)
            
            # إنشاء الفهارس لتحسين الأداء
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_downloads_status ON downloads(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_downloads_created_time ON downloads(created_time)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_speed_stats_timestamp ON speed_stats(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_speed_stats_download_id ON speed_stats(download_id)")
            
            conn.commit()
            
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للوصول للأعمدة بالاسم
        try:
            yield conn
        finally:
            conn.close()
            
    def add_download(self, download_info) -> bool:
        """إضافة تحميل جديد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO downloads (
                        id, url, filename, save_path, total_size, downloaded_size,
                        status, progress, speed, created_time, file_type, priority,
                        resume_supported
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    download_info.id,
                    download_info.url,
                    download_info.filename,
                    download_info.save_path,
                    download_info.total_size,
                    download_info.downloaded_size,
                    download_info.status.value,
                    download_info.progress,
                    download_info.speed,
                    download_info.created_time,
                    download_info.file_type,
                    download_info.priority,
                    download_info.resume_supported
                ))
                conn.commit()
                
                # تحديث الإحصائيات اليومية
                self.update_daily_stats(download_info.created_time, 'added')
                return True
                
        except Exception as e:
            print(f"خطأ في إضافة التحميل: {e}")
            return False
            
    def update_download_progress(self, download_id: str, progress: float, 
                               downloaded_size: int, speed: float) -> bool:
        """تحديث تقدم التحميل"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # تحديث معلومات التحميل
                cursor.execute("""
                    UPDATE downloads 
                    SET progress = ?, downloaded_size = ?, speed = ?
                    WHERE id = ?
                """, (progress, downloaded_size, speed, download_id))
                
                # إضافة إحصائية السرعة
                cursor.execute("""
                    INSERT INTO speed_stats (download_id, timestamp, speed, downloaded_bytes)
                    VALUES (?, ?, ?, ?)
                """, (download_id, time.time(), speed, downloaded_size))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"خطأ في تحديث التقدم: {e}")
            return False
            
    def update_download_status(self, download_id: str, status: str, 
                             error_message: str = "") -> bool:
        """تحديث حالة التحميل"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                update_fields = ["status = ?"]
                params = [status]
                
                if error_message:
                    update_fields.append("error_message = ?")
                    params.append(error_message)
                    
                if status == "downloading":
                    update_fields.append("start_time = ?")
                    params.append(time.time())
                    
                params.append(download_id)
                
                cursor.execute(f"""
                    UPDATE downloads 
                    SET {', '.join(update_fields)}
                    WHERE id = ?
                """, params)
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"خطأ في تحديث الحالة: {e}")
            return False
            
    def complete_download(self, download_id: str) -> bool:
        """إكمال التحميل"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # تحديث معلومات التحميل
                cursor.execute("""
                    UPDATE downloads 
                    SET status = 'completed', end_time = ?, progress = 100.0
                    WHERE id = ?
                """, (time.time(), download_id))
                
                # الحصول على معلومات التحميل للإحصائيات
                cursor.execute("""
                    SELECT file_type, total_size, created_time, 
                           (end_time - start_time) as duration
                    FROM downloads WHERE id = ?
                """, (download_id,))
                
                row = cursor.fetchone()
                if row:
                    # تحديث إحصائيات نوع الملف
                    self.update_file_type_stats(row['file_type'], row['total_size'])
                    
                    # تحديث الإحصائيات اليومية
                    self.update_daily_stats(row['created_time'], 'completed', 
                                          row['total_size'])
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"خطأ في إكمال التحميل: {e}")
            return False
            
    def remove_download(self, download_id: str) -> bool:
        """حذف التحميل"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # حذف إحصائيات السرعة
                cursor.execute("DELETE FROM speed_stats WHERE download_id = ?", 
                             (download_id,))
                
                # حذف التحميل
                cursor.execute("DELETE FROM downloads WHERE id = ?", (download_id,))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"خطأ في حذف التحميل: {e}")
            return False
            
    def get_download(self, download_id: str) -> Optional[Dict]:
        """الحصول على تحميل محدد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM downloads WHERE id = ?", (download_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except Exception as e:
            print(f"خطأ في الحصول على التحميل: {e}")
            return None
            
    def get_all_downloads(self, status: str = None) -> List[Dict]:
        """الحصول على جميع التحميلات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if status:
                    cursor.execute("SELECT * FROM downloads WHERE status = ? ORDER BY created_time DESC", 
                                 (status,))
                else:
                    cursor.execute("SELECT * FROM downloads ORDER BY created_time DESC")
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"خطأ في الحصول على التحميلات: {e}")
            return []
            
    def get_completed_downloads(self, limit: int = 100) -> List[Dict]:
        """الحصول على التحميلات المكتملة"""
        return self.get_all_downloads("completed")[:limit]
        
    def search_downloads(self, query: str) -> List[Dict]:
        """البحث في التحميلات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM downloads 
                    WHERE filename LIKE ? OR url LIKE ?
                    ORDER BY created_time DESC
                """, (f"%{query}%", f"%{query}%"))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []
            
    def update_daily_stats(self, timestamp: float, action: str, bytes_count: int = 0):
        """تحديث الإحصائيات اليومية"""
        try:
            date_str = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # التحقق من وجود السجل
                cursor.execute("SELECT * FROM daily_stats WHERE date = ?", (date_str,))
                row = cursor.fetchone()
                
                if row:
                    # تحديث السجل الموجود
                    if action == 'added':
                        cursor.execute("""
                            UPDATE daily_stats 
                            SET total_downloads = total_downloads + 1
                            WHERE date = ?
                        """, (date_str,))
                    elif action == 'completed':
                        cursor.execute("""
                            UPDATE daily_stats 
                            SET completed_downloads = completed_downloads + 1,
                                total_bytes = total_bytes + ?
                            WHERE date = ?
                        """, (bytes_count, date_str))
                    elif action == 'failed':
                        cursor.execute("""
                            UPDATE daily_stats 
                            SET failed_downloads = failed_downloads + 1
                            WHERE date = ?
                        """, (date_str,))
                else:
                    # إنشاء سجل جديد
                    total_downloads = 1 if action == 'added' else 0
                    completed_downloads = 1 if action == 'completed' else 0
                    failed_downloads = 1 if action == 'failed' else 0
                    total_bytes = bytes_count if action == 'completed' else 0
                    
                    cursor.execute("""
                        INSERT INTO daily_stats 
                        (date, total_downloads, completed_downloads, failed_downloads, total_bytes)
                        VALUES (?, ?, ?, ?, ?)
                    """, (date_str, total_downloads, completed_downloads, failed_downloads, total_bytes))
                
                conn.commit()
                
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات اليومية: {e}")
            
    def update_file_type_stats(self, file_type: str, file_size: int):
        """تحديث إحصائيات نوع الملف"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM file_type_stats WHERE file_type = ?", (file_type,))
                row = cursor.fetchone()
                
                if row:
                    cursor.execute("""
                        UPDATE file_type_stats 
                        SET count = count + 1, total_bytes = total_bytes + ?
                        WHERE file_type = ?
                    """, (file_size, file_type))
                else:
                    cursor.execute("""
                        INSERT INTO file_type_stats (file_type, count, total_bytes)
                        VALUES (?, 1, ?)
                    """, (file_type, file_size))
                
                conn.commit()
                
        except Exception as e:
            print(f"خطأ في تحديث إحصائيات نوع الملف: {e}")
            
    def get_daily_stats(self, days: int = 30) -> List[Dict]:
        """الحصول على الإحصائيات اليومية"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # حساب التاريخ المطلوب
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                
                cursor.execute("""
                    SELECT * FROM daily_stats 
                    WHERE date >= ? 
                    ORDER BY date DESC
                """, (start_date,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات اليومية: {e}")
            return []
            
    def get_monthly_stats(self, months: int = 12) -> List[Dict]:
        """الحصول على الإحصائيات الشهرية"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT 
                        strftime('%Y-%m', datetime(created_time, 'unixepoch')) as month,
                        COUNT(*) as total_downloads,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_downloads,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_downloads,
                        SUM(CASE WHEN status = 'completed' THEN total_size ELSE 0 END) as total_bytes,
                        AVG(CASE WHEN status = 'completed' THEN speed ELSE NULL END) as avg_speed,
                        MAX(speed) as max_speed
                    FROM downloads 
                    WHERE created_time >= ?
                    GROUP BY month
                    ORDER BY month DESC
                """, (time.time() - (months * 30 * 24 * 3600),))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات الشهرية: {e}")
            return []
            
    def get_file_type_stats(self) -> List[Dict]:
        """الحصول على إحصائيات أنواع الملفات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT file_type, COUNT(*) as count, SUM(total_size) as total_bytes
                    FROM downloads 
                    WHERE status = 'completed' AND file_type IS NOT NULL
                    GROUP BY file_type
                    ORDER BY count DESC
                """)
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات أنواع الملفات: {e}")
            return []
            
    def get_speed_history(self, download_id: str) -> List[Tuple[float, float]]:
        """الحصول على تاريخ السرعة لتحميل محدد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT timestamp, speed FROM speed_stats 
                    WHERE download_id = ? 
                    ORDER BY timestamp
                """, (download_id,))
                
                return [(row[0], row[1]) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"خطأ في الحصول على تاريخ السرعة: {e}")
            return []
            
    def get_total_statistics(self) -> Dict:
        """الحصول على الإحصائيات الإجمالية"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # إجمالي التحميلات
                cursor.execute("SELECT COUNT(*) FROM downloads")
                total_downloads = cursor.fetchone()[0]
                
                # التحميلات المكتملة
                cursor.execute("SELECT COUNT(*) FROM downloads WHERE status = 'completed'")
                completed_downloads = cursor.fetchone()[0]
                
                # التحميلات الفاشلة
                cursor.execute("SELECT COUNT(*) FROM downloads WHERE status = 'failed'")
                failed_downloads = cursor.fetchone()[0]
                
                # إجمالي البيانات المحملة
                cursor.execute("SELECT SUM(total_size) FROM downloads WHERE status = 'completed'")
                total_bytes = cursor.fetchone()[0] or 0
                
                # متوسط السرعة
                cursor.execute("SELECT AVG(speed) FROM downloads WHERE status = 'completed' AND speed > 0")
                avg_speed = cursor.fetchone()[0] or 0
                
                # أقصى سرعة
                cursor.execute("SELECT MAX(speed) FROM downloads")
                max_speed = cursor.fetchone()[0] or 0
                
                return {
                    'total_downloads': total_downloads,
                    'completed_downloads': completed_downloads,
                    'failed_downloads': failed_downloads,
                    'success_rate': (completed_downloads / total_downloads * 100) if total_downloads > 0 else 0,
                    'total_bytes': total_bytes,
                    'avg_speed': avg_speed,
                    'max_speed': max_speed
                }
                
        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات الإجمالية: {e}")
            return {}
            
    def cleanup_old_data(self, days: int = 90):
        """تنظيف البيانات القديمة"""
        try:
            cutoff_time = time.time() - (days * 24 * 3600)
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # حذف إحصائيات السرعة القديمة
                cursor.execute("DELETE FROM speed_stats WHERE timestamp < ?", (cutoff_time,))
                
                # حذف التحميلات القديمة المكتملة أو الفاشلة
                cursor.execute("""
                    DELETE FROM downloads 
                    WHERE created_time < ? AND status IN ('completed', 'failed')
                """, (cutoff_time,))
                
                conn.commit()
                print(f"تم تنظيف البيانات الأقدم من {days} يوم")
                
        except Exception as e:
            print(f"خطأ في تنظيف البيانات: {e}")
            
    def export_data(self, file_path: str) -> bool:
        """تصدير البيانات إلى ملف JSON"""
        try:
            data = {
                'downloads': self.get_all_downloads(),
                'daily_stats': self.get_daily_stats(365),
                'file_type_stats': self.get_file_type_stats(),
                'total_stats': self.get_total_statistics(),
                'export_time': time.time()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {e}")
            return False