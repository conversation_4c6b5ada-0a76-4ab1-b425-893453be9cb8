#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التحميلات الرئيسي
Professional Download Manager Core
"""

import asyncio
import aiohttp
import aiofiles
import os
import time
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from urllib.parse import urlparse, unquote
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtWidgets import QMessageBox

from core.database import DatabaseManager
from core.file_utils import FileUtils

class DownloadStatus(Enum):
    """حالات التحميل"""
    WAITING = "waiting"
    DOWNLOADING = "downloading"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class DownloadInfo:
    """معلومات التحميل"""
    id: str = ""
    url: str = ""
    filename: str = ""
    save_path: str = ""
    total_size: int = 0
    downloaded_size: int = 0
    status: DownloadStatus = DownloadStatus.WAITING
    speed: float = 0.0
    progress: float = 0.0
    created_time: float = field(default_factory=time.time)
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: str = ""
    resume_supported: bool = False
    file_type: str = ""
    priority: int = 0
    
    def __post_init__(self):
        if not self.id:
            self.id = f"dl_{int(time.time() * 1000)}"
        if not self.filename and self.url:
            self.filename = self.extract_filename_from_url()
        if not self.file_type and self.filename:
            self.file_type = FileUtils.get_file_type(self.filename)
            
    def extract_filename_from_url(self) -> str:
        """استخراج اسم الملف من الرابط"""
        try:
            parsed_url = urlparse(self.url)
            filename = unquote(os.path.basename(parsed_url.path))
            if not filename or '.' not in filename:
                filename = f"download_{int(time.time())}"
            return filename
        except:
            return f"download_{int(time.time())}"

class DownloadWorker(QObject):
    """عامل التحميل المتوازي"""
    
    # الإشارات
    progress_updated = Signal(str, float, float, int, int)  # id, progress, speed, downloaded, total
    status_changed = Signal(str, str)  # id, status
    download_completed = Signal(str)  # id
    download_failed = Signal(str, str)  # id, error
    
    def __init__(self, download_info: DownloadInfo, max_connections: int = 4):
        super().__init__()
        self.download_info = download_info
        self.max_connections = max_connections
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_paused = False
        self.is_cancelled = False
        self.chunk_size = 8192
        self.speed_samples = []
        self.last_update_time = time.time()
        
    async def start_download(self):
        """بدء التحميل"""
        try:
            self.download_info.status = DownloadStatus.DOWNLOADING
            self.download_info.start_time = time.time()
            self.status_changed.emit(self.download_info.id, "downloading")
            
            # إنشاء جلسة HTTP
            timeout = aiohttp.ClientTimeout(total=None, connect=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # فحص دعم الاستئناف
            await self.check_resume_support()
            
            # بدء التحميل
            if self.download_info.resume_supported and self.download_info.total_size > 1024 * 1024:
                # تحميل متعدد الاتصالات للملفات الكبيرة
                await self.download_with_multiple_connections()
            else:
                # تحميل عادي
                await self.download_single_connection()
                
            # إكمال التحميل
            self.download_info.status = DownloadStatus.COMPLETED
            self.download_info.end_time = time.time()
            self.download_completed.emit(self.download_info.id)
            
        except Exception as e:
            self.download_info.status = DownloadStatus.FAILED
            self.download_info.error_message = str(e)
            self.download_failed.emit(self.download_info.id, str(e))
        finally:
            if self.session:
                await self.session.close()
                
    async def check_resume_support(self):
        """فحص دعم الاستئناف"""
        try:
            headers = {'Range': 'bytes=0-1'}
            async with self.session.head(self.download_info.url, headers=headers) as response:
                if response.status == 206:  # Partial Content
                    self.download_info.resume_supported = True
                    # الحصول على حجم الملف
                    content_range = response.headers.get('Content-Range', '')
                    if content_range:
                        total_size = content_range.split('/')[-1]
                        if total_size.isdigit():
                            self.download_info.total_size = int(total_size)
                else:
                    # محاولة الحصول على حجم الملف من Content-Length
                    async with self.session.head(self.download_info.url) as head_response:
                        content_length = head_response.headers.get('Content-Length')
                        if content_length and content_length.isdigit():
                            self.download_info.total_size = int(content_length)
        except Exception as e:
            print(f"خطأ في فحص دعم الاستئناف: {e}")
            
    async def download_single_connection(self):
        """تحميل بإتصال واحد"""
        headers = {}
        if self.download_info.downloaded_size > 0 and self.download_info.resume_supported:
            headers['Range'] = f'bytes={self.download_info.downloaded_size}-'
            
        async with self.session.get(self.download_info.url, headers=headers) as response:
            if response.status not in [200, 206]:
                raise Exception(f"HTTP Error: {response.status}")
                
            # إنشاء مجلد الحفظ
            save_dir = Path(self.download_info.save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # فتح الملف للكتابة
            mode = 'ab' if self.download_info.downloaded_size > 0 else 'wb'
            async with aiofiles.open(self.download_info.save_path, mode) as file:
                async for chunk in response.content.iter_chunked(self.chunk_size):
                    if self.is_cancelled:
                        break
                        
                    while self.is_paused:
                        await asyncio.sleep(0.1)
                        
                    await file.write(chunk)
                    self.download_info.downloaded_size += len(chunk)
                    
                    # تحديث التقدم والسرعة
                    await self.update_progress()
                    
    async def download_with_multiple_connections(self):
        """تحميل متعدد الاتصالات"""
        if not self.download_info.total_size:
            await self.download_single_connection()
            return
            
        # تقسيم الملف إلى أجزاء
        chunk_size = self.download_info.total_size // self.max_connections
        tasks = []
        
        for i in range(self.max_connections):
            start = i * chunk_size
            end = start + chunk_size - 1 if i < self.max_connections - 1 else self.download_info.total_size - 1
            
            task = asyncio.create_task(
                self.download_chunk(start, end, f"{self.download_info.save_path}.part{i}")
            )
            tasks.append(task)
            
        # انتظار اكتمال جميع الأجزاء
        await asyncio.gather(*tasks)
        
        # دمج الأجزاء
        await self.merge_chunks()
        
    async def download_chunk(self, start: int, end: int, temp_path: str):
        """تحميل جزء من الملف"""
        headers = {'Range': f'bytes={start}-{end}'}
        
        async with self.session.get(self.download_info.url, headers=headers) as response:
            if response.status != 206:
                raise Exception(f"Chunk download failed: {response.status}")
                
            async with aiofiles.open(temp_path, 'wb') as file:
                async for chunk in response.content.iter_chunked(self.chunk_size):
                    if self.is_cancelled:
                        break
                        
                    while self.is_paused:
                        await asyncio.sleep(0.1)
                        
                    await file.write(chunk)
                    self.download_info.downloaded_size += len(chunk)
                    
                    # تحديث التقدم
                    await self.update_progress()
                    
    async def merge_chunks(self):
        """دمج أجزاء الملف"""
        async with aiofiles.open(self.download_info.save_path, 'wb') as output_file:
            for i in range(self.max_connections):
                chunk_path = f"{self.download_info.save_path}.part{i}"
                if os.path.exists(chunk_path):
                    async with aiofiles.open(chunk_path, 'rb') as chunk_file:
                        while True:
                            chunk = await chunk_file.read(self.chunk_size)
                            if not chunk:
                                break
                            await output_file.write(chunk)
                    
                    # حذف الملف المؤقت
                    os.remove(chunk_path)
                    
    async def update_progress(self):
        """تحديث التقدم والسرعة"""
        current_time = time.time()
        
        if self.download_info.total_size > 0:
            self.download_info.progress = (self.download_info.downloaded_size / self.download_info.total_size) * 100
        
        # حساب السرعة
        time_diff = current_time - self.last_update_time
        if time_diff >= 1.0:  # تحديث كل ثانية
            if hasattr(self, 'last_downloaded_size'):
                bytes_diff = self.download_info.downloaded_size - self.last_downloaded_size
                speed = bytes_diff / time_diff
                
                # حفظ عينات السرعة للحصول على متوسط
                self.speed_samples.append(speed)
                if len(self.speed_samples) > 10:
                    self.speed_samples.pop(0)
                    
                self.download_info.speed = sum(self.speed_samples) / len(self.speed_samples)
            
            self.last_downloaded_size = self.download_info.downloaded_size
            self.last_update_time = current_time
            
            # إرسال إشارة التحديث
            self.progress_updated.emit(
                self.download_info.id,
                self.download_info.progress,
                self.download_info.speed,
                self.download_info.downloaded_size,
                self.download_info.total_size
            )
            
    def pause(self):
        """إيقاف التحميل"""
        self.is_paused = True
        self.download_info.status = DownloadStatus.PAUSED
        self.status_changed.emit(self.download_info.id, "paused")
        
    def resume(self):
        """استئناف التحميل"""
        self.is_paused = False
        self.download_info.status = DownloadStatus.DOWNLOADING
        self.status_changed.emit(self.download_info.id, "downloading")
        
    def cancel(self):
        """إلغاء التحميل"""
        self.is_cancelled = True
        self.download_info.status = DownloadStatus.CANCELLED
        self.status_changed.emit(self.download_info.id, "cancelled")

class DownloadManager(QObject):
    """مدير التحميلات الرئيسي"""
    
    # الإشارات
    download_added = Signal(str)  # download_id
    download_progress = Signal(str, float, float, int, int)  # id, progress, speed, downloaded, total
    download_status_changed = Signal(str, str)  # id, status
    download_completed = Signal(str)  # id
    download_failed = Signal(str, str)  # id, error
    
    def __init__(self):
        super().__init__()
        self.downloads: Dict[str, DownloadInfo] = {}
        self.workers: Dict[str, DownloadWorker] = {}
        self.threads: Dict[str, QThread] = {}
        self.db_manager = DatabaseManager()
        self.max_concurrent_downloads = 3
        self.active_downloads = 0
        self.download_queue = []
        
        # تايمر لمعالجة قائمة الانتظار
        self.queue_timer = QTimer()
        self.queue_timer.timeout.connect(self.process_queue)
        self.queue_timer.start(1000)
        
    def add_download(self, download_info: DownloadInfo) -> str:
        """إضافة تحميل جديد"""
        # حفظ في قاعدة البيانات
        self.db_manager.add_download(download_info)
        
        # إضافة إلى القائمة
        self.downloads[download_info.id] = download_info
        
        # إضافة إلى قائمة الانتظار
        self.download_queue.append(download_info.id)
        self.download_queue.sort(key=lambda x: self.downloads[x].priority, reverse=True)
        
        self.download_added.emit(download_info.id)
        return download_info.id
        
    def process_queue(self):
        """معالجة قائمة الانتظار"""
        if self.active_downloads >= self.max_concurrent_downloads:
            return
            
        if not self.download_queue:
            return
            
        # بدء التحميل التالي في القائمة
        download_id = self.download_queue.pop(0)
        if download_id in self.downloads:
            self.start_download(download_id)
            
    def start_download(self, download_id: str):
        """بدء تحميل محدد"""
        if download_id not in self.downloads:
            return
            
        download_info = self.downloads[download_id]
        
        # إنشاء عامل التحميل
        worker = DownloadWorker(download_info)
        thread = QThread()
        
        # ربط الإشارات
        worker.progress_updated.connect(self.on_progress_updated)
        worker.status_changed.connect(self.on_status_changed)
        worker.download_completed.connect(self.on_download_completed)
        worker.download_failed.connect(self.on_download_failed)
        
        # نقل العامل إلى الخيط
        worker.moveToThread(thread)
        thread.started.connect(lambda: asyncio.run(worker.start_download()))
        
        # حفظ المراجع
        self.workers[download_id] = worker
        self.threads[download_id] = thread
        
        # بدء الخيط
        thread.start()
        self.active_downloads += 1
        
    def pause_download(self, download_id: str):
        """إيقاف تحميل محدد"""
        if download_id in self.workers:
            self.workers[download_id].pause()
            
    def resume_download(self, download_id: str):
        """استئناف تحميل محدد"""
        if download_id in self.workers:
            self.workers[download_id].resume()
        elif download_id in self.downloads:
            # إعادة إضافة إلى قائمة الانتظار
            if download_id not in self.download_queue:
                self.download_queue.append(download_id)
                
    def cancel_download(self, download_id: str):
        """إلغاء تحميل محدد"""
        if download_id in self.workers:
            self.workers[download_id].cancel()
            
    def remove_download(self, download_id: str):
        """حذف تحميل"""
        # إلغاء التحميل أولاً
        self.cancel_download(download_id)
        
        # حذف من القوائم
        if download_id in self.downloads:
            del self.downloads[download_id]
        if download_id in self.workers:
            del self.workers[download_id]
        if download_id in self.threads:
            self.threads[download_id].quit()
            self.threads[download_id].wait()
            del self.threads[download_id]
        if download_id in self.download_queue:
            self.download_queue.remove(download_id)
            
        # حذف من قاعدة البيانات
        self.db_manager.remove_download(download_id)
        
    def pause_all(self):
        """إيقاف جميع التحميلات"""
        for download_id in list(self.workers.keys()):
            self.pause_download(download_id)
            
    def resume_all(self):
        """استئناف جميع التحميلات"""
        for download_id in list(self.downloads.keys()):
            if self.downloads[download_id].status == DownloadStatus.PAUSED:
                self.resume_download(download_id)
                
    def get_download_info(self, download_id: str) -> Optional[DownloadInfo]:
        """الحصول على معلومات التحميل"""
        return self.downloads.get(download_id)
        
    def get_all_downloads(self) -> List[DownloadInfo]:
        """الحصول على جميع التحميلات"""
        return list(self.downloads.values())
        
    def get_total_speed(self) -> float:
        """الحصول على السرعة الإجمالية"""
        total_speed = 0
        for download in self.downloads.values():
            if download.status == DownloadStatus.DOWNLOADING:
                total_speed += download.speed
        return total_speed
        
    def on_progress_updated(self, download_id: str, progress: float, speed: float, downloaded: int, total: int):
        """عند تحديث التقدم"""
        if download_id in self.downloads:
            download = self.downloads[download_id]
            download.progress = progress
            download.speed = speed
            download.downloaded_size = downloaded
            download.total_size = total
            
            # تحديث قاعدة البيانات
            self.db_manager.update_download_progress(download_id, progress, downloaded, speed)
            
        self.download_progress.emit(download_id, progress, speed, downloaded, total)
        
    def on_status_changed(self, download_id: str, status: str):
        """عند تغيير الحالة"""
        if download_id in self.downloads:
            self.downloads[download_id].status = DownloadStatus(status)
            self.db_manager.update_download_status(download_id, status)
            
        self.download_status_changed.emit(download_id, status)
        
    def on_download_completed(self, download_id: str):
        """عند اكتمال التحميل"""
        if download_id in self.downloads:
            download = self.downloads[download_id]
            download.status = DownloadStatus.COMPLETED
            download.end_time = time.time()
            
            # تحديث قاعدة البيانات
            self.db_manager.complete_download(download_id)
            
            # فتح الملف تلقائياً إذا كان مفعلاً
            self.open_completed_file(download.save_path)
            
        # تنظيف الموارد
        self.cleanup_download(download_id)
        self.download_completed.emit(download_id)
        
    def on_download_failed(self, download_id: str, error: str):
        """عند فشل التحميل"""
        if download_id in self.downloads:
            download = self.downloads[download_id]
            download.status = DownloadStatus.FAILED
            download.error_message = error
            
            # تحديث قاعدة البيانات
            self.db_manager.update_download_status(download_id, "failed", error)
            
        # تنظيف الموارد
        self.cleanup_download(download_id)
        self.download_failed.emit(download_id, error)
        
    def cleanup_download(self, download_id: str):
        """تنظيف موارد التحميل"""
        if download_id in self.threads:
            self.threads[download_id].quit()
            self.threads[download_id].wait()
            del self.threads[download_id]
            
        if download_id in self.workers:
            del self.workers[download_id]
            
        self.active_downloads = max(0, self.active_downloads - 1)
        
    def open_completed_file(self, file_path: str):
        """فتح الملف المكتمل"""
        try:
            import subprocess
            import os
            
            if os.path.exists(file_path):
                if os.name == 'nt':  # Windows
                    os.startfile(file_path)
                else:  # Linux/Mac
                    subprocess.run(['xdg-open', file_path])
        except Exception as e:
            print(f"خطأ في فتح الملف: {e}")