#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات تحليل الملفات ونوعها
File Analysis and Type Detection Utilities
"""

import os
import mimetypes
from pathlib import Path
from typing import Dict, Optional, Tuple
from urllib.parse import urlparse, unquote

class FileUtils:
    """أدوات تحليل الملفات"""
    
    # قاموس أنواع الملفات مع الأيقونات والأوصاف
    FILE_TYPES = {
        # ملفات الفيديو
        'video': {
            'extensions': ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp'],
            'icon': 'video.png',
            'description': 'ملف فيديو',
            'category': 'multimedia'
        },
        
        # ملفات الصوت
        'audio': {
            'extensions': ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'],
            'icon': 'audio.png',
            'description': 'ملف صوتي',
            'category': 'multimedia'
        },
        
        # ملفات الصور
        'image': {
            'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.svg', '.webp'],
            'icon': 'image.png',
            'description': 'ملف صورة',
            'category': 'multimedia'
        },
        
        # ملفات المستندات
        'document': {
            'extensions': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'],
            'icon': 'document.png',
            'description': 'مستند',
            'category': 'document'
        },
        
        # ملفات جداول البيانات
        'spreadsheet': {
            'extensions': ['.xls', '.xlsx', '.csv', '.ods'],
            'icon': 'spreadsheet.png',
            'description': 'جدول بيانات',
            'category': 'document'
        },
        
        # ملفات العروض التقديمية
        'presentation': {
            'extensions': ['.ppt', '.pptx', '.odp'],
            'icon': 'presentation.png',
            'description': 'عرض تقديمي',
            'category': 'document'
        },
        
        # ملفات الأرشيف
        'archive': {
            'extensions': ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'],
            'icon': 'archive.png',
            'description': 'ملف مضغوط',
            'category': 'archive'
        },
        
        # ملفات البرمجة
        'code': {
            'extensions': ['.py', '.js', '.html', '.css', '.cpp', '.c', '.java', '.php', '.rb'],
            'icon': 'code.png',
            'description': 'ملف برمجي',
            'category': 'development'
        },
        
        # ملفات التطبيقات
        'executable': {
            'extensions': ['.exe', '.msi', '.deb', '.rpm', '.dmg', '.app'],
            'icon': 'executable.png',
            'description': 'ملف تنفيذي',
            'category': 'application'
        },
        
        # ملفات الخطوط
        'font': {
            'extensions': ['.ttf', '.otf', '.woff', '.woff2', '.eot'],
            'icon': 'font.png',
            'description': 'ملف خط',
            'category': 'design'
        },
        
        # ملفات التصميم
        'design': {
            'extensions': ['.psd', '.ai', '.sketch', '.fig', '.xd'],
            'icon': 'design.png',
            'description': 'ملف تصميم',
            'category': 'design'
        }
    }
    
    @classmethod
    def get_file_type(cls, filename: str) -> str:
        """تحديد نوع الملف من اسمه"""
        if not filename:
            return 'unknown'
            
        # الحصول على الامتداد
        extension = Path(filename).suffix.lower()
        
        # البحث في قاموس أنواع الملفات
        for file_type, info in cls.FILE_TYPES.items():
            if extension in info['extensions']:
                return file_type
                
        return 'unknown'
    
    @classmethod
    def get_file_type_from_mime(cls, mime_type: str) -> str:
        """تحديد نوع الملف من MIME type"""
        if not mime_type:
            return 'unknown'
            
        mime_type = mime_type.lower()
        
        # تصنيف حسب MIME type
        if mime_type.startswith('video/'):
            return 'video'
        elif mime_type.startswith('audio/'):
            return 'audio'
        elif mime_type.startswith('image/'):
            return 'image'
        elif mime_type in ['application/pdf']:
            return 'document'
        elif mime_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            return 'document'
        elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            return 'spreadsheet'
        elif mime_type in ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation']:
            return 'presentation'
        elif mime_type in ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed']:
            return 'archive'
        elif mime_type in ['application/x-executable', 'application/x-msdownload']:
            return 'executable'
        elif mime_type.startswith('text/'):
            return 'document'
            
        return 'unknown'
    
    @classmethod
    def get_file_info(cls, filename: str) -> Dict:
        """الحصول على معلومات شاملة عن الملف"""
        file_type = cls.get_file_type(filename)
        
        info = {
            'filename': filename,
            'extension': Path(filename).suffix.lower(),
            'type': file_type,
            'icon': cls.FILE_TYPES.get(file_type, {}).get('icon', 'unknown.png'),
            'description': cls.FILE_TYPES.get(file_type, {}).get('description', 'ملف غير معروف'),
            'category': cls.FILE_TYPES.get(file_type, {}).get('category', 'other')
        }
        
        return info
    
    @classmethod
    def extract_filename_from_url(cls, url: str) -> str:
        """استخراج اسم الملف من الرابط"""
        try:
            parsed_url = urlparse(url)
            filename = unquote(os.path.basename(parsed_url.path))
            
            # إذا لم يكن هناك اسم ملف واضح
            if not filename or '.' not in filename:
                # محاولة استخراج من query parameters
                from urllib.parse import parse_qs
                query_params = parse_qs(parsed_url.query)
                
                # البحث عن معاملات شائعة لأسماء الملفات
                for param in ['filename', 'file', 'name', 'title']:
                    if param in query_params:
                        potential_filename = query_params[param][0]
                        if '.' in potential_filename:
                            filename = potential_filename
                            break
                
                # إذا لم نجد اسم ملف، إنشاء اسم افتراضي
                if not filename or '.' not in filename:
                    import time
                    filename = f"download_{int(time.time())}"
                    
            return filename
            
        except Exception as e:
            print(f"خطأ في استخراج اسم الملف: {e}")
            import time
            return f"download_{int(time.time())}"
    
    @classmethod
    def get_mime_type(cls, filename: str) -> Optional[str]:
        """الحصول على MIME type للملف"""
        try:
            mime_type, _ = mimetypes.guess_type(filename)
            return mime_type
        except:
            return None
    
    @classmethod
    def format_file_size(cls, size_bytes: int) -> str:
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
            
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
            
        return f"{size:.1f} {size_names[i]}"
    
    @classmethod
    def format_speed(cls, speed_bytes_per_sec: float) -> str:
        """تنسيق السرعة"""
        return f"{cls.format_file_size(int(speed_bytes_per_sec))}/s"
    
    @classmethod
    def format_time(cls, seconds: float) -> str:
        """تنسيق الوقت"""
        if seconds < 60:
            return f"{int(seconds)} ثانية"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes} دقيقة {secs} ثانية"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours} ساعة {minutes} دقيقة"
    
    @classmethod
    def calculate_eta(cls, downloaded_size: int, total_size: int, speed: float) -> str:
        """حساب الوقت المتبقي"""
        if speed <= 0 or total_size <= downloaded_size:
            return "غير معروف"
            
        remaining_bytes = total_size - downloaded_size
        eta_seconds = remaining_bytes / speed
        
        return cls.format_time(eta_seconds)
    
    @classmethod
    def is_valid_url(cls, url: str) -> bool:
        """التحقق من صحة الرابط"""
        try:
            parsed = urlparse(url)
            return bool(parsed.scheme and parsed.netloc)
        except:
            return False
    
    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """تنظيف اسم الملف من الأحرف غير المسموحة"""
        # الأحرف غير المسموحة في Windows
        invalid_chars = '<>:"/\\|?*'
        
        for char in invalid_chars:
            filename = filename.replace(char, '_')
            
        # إزالة المسافات الزائدة
        filename = filename.strip()
        
        # التأكد من أن الاسم ليس فارغاً
        if not filename:
            import time
            filename = f"download_{int(time.time())}"
            
        return filename
    
    @classmethod
    def get_safe_filename(cls, url: str, suggested_name: str = "") -> str:
        """الحصول على اسم ملف آمن"""
        if suggested_name:
            filename = suggested_name
        else:
            filename = cls.extract_filename_from_url(url)
            
        return cls.sanitize_filename(filename)
    
    @classmethod
    def get_unique_filename(cls, directory: str, filename: str) -> str:
        """الحصول على اسم ملف فريد في المجلد"""
        base_path = Path(directory) / filename
        
        if not base_path.exists():
            return str(base_path)
            
        # إضافة رقم للاسم
        name = base_path.stem
        extension = base_path.suffix
        counter = 1
        
        while True:
            new_filename = f"{name} ({counter}){extension}"
            new_path = Path(directory) / new_filename
            
            if not new_path.exists():
                return str(new_path)
                
            counter += 1
    
    @classmethod
    def get_download_categories(cls) -> Dict[str, list]:
        """الحصول على تصنيفات التحميلات"""
        categories = {}
        
        for file_type, info in cls.FILE_TYPES.items():
            category = info['category']
            if category not in categories:
                categories[category] = []
            categories[category].append({
                'type': file_type,
                'description': info['description'],
                'extensions': info['extensions']
            })
            
        return categories
    
    @classmethod
    def get_file_icon_path(cls, filename: str) -> str:
        """الحصول على مسار أيقونة الملف"""
        file_type = cls.get_file_type(filename)
        icon_name = cls.FILE_TYPES.get(file_type, {}).get('icon', 'unknown.png')
        return f"assets/icons/filetypes/{icon_name}"
    
    @classmethod
    def is_media_file(cls, filename: str) -> bool:
        """التحقق من كون الملف ملف وسائط"""
        file_type = cls.get_file_type(filename)
        return file_type in ['video', 'audio', 'image']
    
    @classmethod
    def is_archive_file(cls, filename: str) -> bool:
        """التحقق من كون الملف أرشيف"""
        return cls.get_file_type(filename) == 'archive'
    
    @classmethod
    def is_executable_file(cls, filename: str) -> bool:
        """التحقق من كون الملف تنفيذي"""
        return cls.get_file_type(filename) == 'executable'
    
    @classmethod
    def get_file_priority(cls, filename: str) -> int:
        """تحديد أولوية التحميل حسب نوع الملف"""
        file_type = cls.get_file_type(filename)
        
        # أولويات مختلفة لأنواع الملفات
        priorities = {
            'executable': 5,  # أولوية عالية للتطبيقات
            'archive': 4,     # أولوية عالية للأرشيف
            'video': 3,       # أولوية متوسطة للفيديو
            'audio': 3,       # أولوية متوسطة للصوت
            'document': 2,    # أولوية منخفضة للمستندات
            'image': 1,       # أولوية منخفضة للصور
        }
        
        return priorities.get(file_type, 0)