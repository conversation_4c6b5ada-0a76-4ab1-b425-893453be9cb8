#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة بيانات مبسط
"""

import sqlite3
import time
from pathlib import Path
from contextlib import contextmanager

class SimpleDatabaseManager:
    """مدير قاعدة بيانات مبسط"""
    
    def __init__(self, db_path: str = "downloads.db"):
        self.db_path = Path(db_path)
        self.init_database()
        
    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # جدول التحميلات البسيط
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS downloads (
                        id TEXT PRIMARY KEY,
                        url TEXT NOT NULL,
                        filename TEXT NOT NULL,
                        status TEXT DEFAULT 'waiting',
                        created_time REAL NOT NULL
                    )
                """)
                
                conn.commit()
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def add_download(self, download_id: str, url: str, filename: str):
        """إضافة تحميل جديد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO downloads (id, url, filename, created_time)
                    VALUES (?, ?, ?, ?)
                """, (download_id, url, filename, time.time()))
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في إضافة التحميل: {e}")
            return False
    
    def get_downloads(self):
        """الحصول على جميع التحميلات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM downloads ORDER BY created_time DESC")
                return cursor.fetchall()
        except Exception as e:
            print(f"خطأ في جلب التحميلات: {e}")
            return []