#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الثيمات والألوان
Theme and Color Management System
"""

from typing import Dict, Optional
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QPalette, QColor

class ThemeManager(QObject):
    """مدير الثيمات"""
    
    theme_changed = Signal(str)  # اسم الثيم الجديد
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.is_dark_mode = False
        
        # تعريف الثيمات
        self.themes = {
            "light": self.get_light_theme(),
            "dark": self.get_dark_theme(),
            "blue": self.get_blue_theme(),
            "green": self.get_green_theme()
        }
        
    def get_light_theme(self) -> Dict[str, str]:
        """الثيم الفاتح"""
        return {
            # الألوان الأساسية
            "primary": "#2196F3",
            "primary_dark": "#1976D2", 
            "primary_light": "#BBDEFB",
            "secondary": "#FF9800",
            "accent": "#4CAF50",
            
            # ألوان الخلفية
            "background": "#FFFFFF",
            "surface": "#F5F5F5",
            "card": "#FFFFFF",
            "dialog": "#FFFFFF",
            
            # ألوان النص
            "text_primary": "#212121",
            "text_secondary": "#757575",
            "text_disabled": "#BDBDBD",
            "text_hint": "#9E9E9E",
            
            # ألوان الحدود
            "border": "#E0E0E0",
            "divider": "#EEEEEE",
            
            # ألوان الحالة
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "info": "#2196F3",
            
            # ألوان التحميل
            "download_active": "#2196F3",
            "download_paused": "#FF9800",
            "download_completed": "#4CAF50",
            "download_failed": "#F44336",
            "download_waiting": "#9E9E9E",
            
            # ألوان الأزرار
            "button_primary": "#2196F3",
            "button_primary_hover": "#1976D2",
            "button_secondary": "#757575",
            "button_secondary_hover": "#616161",
            
            # ألوان شريط التقدم
            "progress_background": "#E0E0E0",
            "progress_chunk": "#2196F3",
            
            # ألوان التبويبات
            "tab_active": "#2196F3",
            "tab_inactive": "#757575",
            "tab_background": "#F5F5F5",
            
            # ألوان القوائم
            "menu_background": "#FFFFFF",
            "menu_hover": "#F5F5F5",
            "menu_selected": "#E3F2FD",
            
            # ألوان شريط الأدوات
            "toolbar_background": "#FAFAFA",
            "toolbar_border": "#E0E0E0",
            
            # ألوان شريط الحالة
            "statusbar_background": "#F5F5F5",
            "statusbar_text": "#757575"
        }
        
    def get_dark_theme(self) -> Dict[str, str]:
        """الثيم المظلم"""
        return {
            # الألوان الأساسية
            "primary": "#64B5F6",
            "primary_dark": "#42A5F5",
            "primary_light": "#90CAF9",
            "secondary": "#FFB74D",
            "accent": "#81C784",
            
            # ألوان الخلفية
            "background": "#121212",
            "surface": "#1E1E1E",
            "card": "#2D2D2D",
            "dialog": "#2D2D2D",
            
            # ألوان النص
            "text_primary": "#FFFFFF",
            "text_secondary": "#B3B3B3",
            "text_disabled": "#666666",
            "text_hint": "#808080",
            
            # ألوان الحدود
            "border": "#404040",
            "divider": "#333333",
            
            # ألوان الحالة
            "success": "#81C784",
            "warning": "#FFB74D",
            "error": "#E57373",
            "info": "#64B5F6",
            
            # ألوان التحميل
            "download_active": "#64B5F6",
            "download_paused": "#FFB74D",
            "download_completed": "#81C784",
            "download_failed": "#E57373",
            "download_waiting": "#808080",
            
            # ألوان الأزرار
            "button_primary": "#64B5F6",
            "button_primary_hover": "#42A5F5",
            "button_secondary": "#B3B3B3",
            "button_secondary_hover": "#CCCCCC",
            
            # ألوان شريط التقدم
            "progress_background": "#404040",
            "progress_chunk": "#64B5F6",
            
            # ألوان التبويبات
            "tab_active": "#64B5F6",
            "tab_inactive": "#B3B3B3",
            "tab_background": "#1E1E1E",
            
            # ألوان القوائم
            "menu_background": "#2D2D2D",
            "menu_hover": "#404040",
            "menu_selected": "#1A237E",
            
            # ألوان شريط الأدوات
            "toolbar_background": "#2D2D2D",
            "toolbar_border": "#404040",
            
            # ألوان شريط الحالة
            "statusbar_background": "#1E1E1E",
            "statusbar_text": "#B3B3B3"
        }
        
    def get_blue_theme(self) -> Dict[str, str]:
        """الثيم الأزرق"""
        base = self.get_light_theme()
        base.update({
            "primary": "#1565C0",
            "primary_dark": "#0D47A1",
            "primary_light": "#42A5F5",
            "accent": "#FF5722",
            "download_active": "#1565C0",
            "button_primary": "#1565C0",
            "button_primary_hover": "#0D47A1",
            "tab_active": "#1565C0",
            "progress_chunk": "#1565C0"
        })
        return base
        
    def get_green_theme(self) -> Dict[str, str]:
        """الثيم الأخضر"""
        base = self.get_light_theme()
        base.update({
            "primary": "#388E3C",
            "primary_dark": "#2E7D32",
            "primary_light": "#81C784",
            "accent": "#FF5722",
            "download_active": "#388E3C",
            "button_primary": "#388E3C",
            "button_primary_hover": "#2E7D32",
            "tab_active": "#388E3C",
            "progress_chunk": "#388E3C"
        })
        return base
        
    def set_theme(self, theme_name: str):
        """تعيين الثيم"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.is_dark_mode = theme_name == "dark"
            self.theme_changed.emit(theme_name)
            
    def set_dark_mode(self, enabled: bool):
        """تفعيل/إلغاء الوضع المظلم"""
        self.is_dark_mode = enabled
        if enabled:
            self.set_theme("dark")
        else:
            self.set_theme("light")
            
    def get_current_theme(self) -> Dict[str, str]:
        """الحصول على الثيم الحالي"""
        return self.themes[self.current_theme]
        
    def get_color(self, color_name: str) -> str:
        """الحصول على لون محدد"""
        theme = self.get_current_theme()
        return theme.get(color_name, "#000000")
        
    def get_stylesheet(self) -> str:
        """الحصول على stylesheet للثيم الحالي"""
        theme = self.get_current_theme()
        
        return f"""
        /* النافذة الرئيسية */
        QMainWindow {{
            background-color: {theme['background']};
            color: {theme['text_primary']};
        }}
        
        /* الويدجت المركزي */
        QWidget {{
            background-color: {theme['background']};
            color: {theme['text_primary']};
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 9pt;
        }}
        
        /* الأزرار */
        QPushButton {{
            background-color: {theme['button_primary']};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {theme['button_primary_hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {theme['primary_dark']};
        }}
        
        QPushButton:disabled {{
            background-color: {theme['text_disabled']};
            color: {theme['text_hint']};
        }}
        
        /* الأزرار الثانوية */
        QPushButton[class="secondary"] {{
            background-color: {theme['button_secondary']};
            color: {theme['text_primary']};
        }}
        
        QPushButton[class="secondary"]:hover {{
            background-color: {theme['button_secondary_hover']};
        }}
        
        /* حقول النص */
        QLineEdit {{
            background-color: {theme['surface']};
            border: 2px solid {theme['border']};
            border-radius: 4px;
            padding: 8px;
            color: {theme['text_primary']};
        }}
        
        QLineEdit:focus {{
            border-color: {theme['primary']};
        }}
        
        QTextEdit {{
            background-color: {theme['surface']};
            border: 2px solid {theme['border']};
            border-radius: 4px;
            padding: 8px;
            color: {theme['text_primary']};
        }}
        
        /* القوائم المنسدلة */
        QComboBox {{
            background-color: {theme['surface']};
            border: 2px solid {theme['border']};
            border-radius: 4px;
            padding: 8px;
            color: {theme['text_primary']};
        }}
        
        QComboBox:hover {{
            border-color: {theme['primary']};
        }}
        
        QComboBox::drop-down {{
            border: none;
        }}
        
        QComboBox::down-arrow {{
            image: url(assets/icons/arrow_down.png);
            width: 12px;
            height: 12px;
        }}
        
        /* القوائم */
        QListWidget {{
            background-color: {theme['surface']};
            border: 1px solid {theme['border']};
            border-radius: 4px;
            color: {theme['text_primary']};
        }}
        
        QListWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {theme['divider']};
        }}
        
        QListWidget::item:selected {{
            background-color: {theme['menu_selected']};
        }}
        
        QListWidget::item:hover {{
            background-color: {theme['menu_hover']};
        }}
        
        /* التبويبات */
        QTabWidget::pane {{
            border: 1px solid {theme['border']};
            background-color: {theme['surface']};
        }}
        
        QTabBar::tab {{
            background-color: {theme['tab_background']};
            color: {theme['tab_inactive']};
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {theme['surface']};
            color: {theme['tab_active']};
            border-bottom: 2px solid {theme['tab_active']};
        }}
        
        QTabBar::tab:hover {{
            background-color: {theme['menu_hover']};
        }}
        
        /* شريط التقدم */
        QProgressBar {{
            background-color: {theme['progress_background']};
            border: 1px solid {theme['border']};
            border-radius: 4px;
            text-align: center;
            color: {theme['text_primary']};
        }}
        
        QProgressBar::chunk {{
            background-color: {theme['progress_chunk']};
            border-radius: 3px;
        }}
        
        /* شريط الأدوات */
        QToolBar {{
            background-color: {theme['toolbar_background']};
            border-bottom: 1px solid {theme['toolbar_border']};
            spacing: 4px;
            padding: 4px;
        }}
        
        /* شريط الحالة */
        QStatusBar {{
            background-color: {theme['statusbar_background']};
            color: {theme['statusbar_text']};
            border-top: 1px solid {theme['border']};
        }}
        
        /* القوائم المنبثقة */
        QMenu {{
            background-color: {theme['menu_background']};
            border: 1px solid {theme['border']};
            color: {theme['text_primary']};
        }}
        
        QMenu::item {{
            padding: 8px 20px;
        }}
        
        QMenu::item:selected {{
            background-color: {theme['menu_selected']};
        }}
        
        /* مربعات الاختيار */
        QCheckBox {{
            color: {theme['text_primary']};
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 16px;
            height: 16px;
        }}
        
        QCheckBox::indicator:unchecked {{
            border: 2px solid {theme['border']};
            background-color: {theme['surface']};
            border-radius: 2px;
        }}
        
        QCheckBox::indicator:checked {{
            border: 2px solid {theme['primary']};
            background-color: {theme['primary']};
            border-radius: 2px;
        }}
        
        /* أزرار الراديو */
        QRadioButton {{
            color: {theme['text_primary']};
            spacing: 8px;
        }}
        
        QRadioButton::indicator {{
            width: 16px;
            height: 16px;
        }}
        
        QRadioButton::indicator:unchecked {{
            border: 2px solid {theme['border']};
            background-color: {theme['surface']};
            border-radius: 8px;
        }}
        
        QRadioButton::indicator:checked {{
            border: 2px solid {theme['primary']};
            background-color: {theme['primary']};
            border-radius: 8px;
        }}
        
        /* المنزلقات */
        QSlider::groove:horizontal {{
            border: 1px solid {theme['border']};
            height: 6px;
            background: {theme['progress_background']};
            border-radius: 3px;
        }}
        
        QSlider::handle:horizontal {{
            background: {theme['primary']};
            border: 1px solid {theme['primary_dark']};
            width: 16px;
            margin: -6px 0;
            border-radius: 8px;
        }}
        
        QSlider::sub-page:horizontal {{
            background: {theme['primary']};
            border-radius: 3px;
        }}
        
        /* أشرطة التمرير */
        QScrollBar:vertical {{
            background: {theme['surface']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background: {theme['border']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: {theme['text_secondary']};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        /* المجموعات */
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {theme['border']};
            border-radius: 4px;
            margin-top: 10px;
            color: {theme['text_primary']};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: {theme['background']};
        }}
        
        /* الحوارات */
        QDialog {{
            background-color: {theme['dialog']};
            color: {theme['text_primary']};
        }}
        
        /* التلميحات */
        QToolTip {{
            background-color: {theme['card']};
            color: {theme['text_primary']};
            border: 1px solid {theme['border']};
            padding: 4px;
            border-radius: 4px;
        }}
        
        /* الفواصل */
        QSplitter::handle {{
            background-color: {theme['border']};
        }}
        
        QSplitter::handle:horizontal {{
            width: 2px;
        }}
        
        QSplitter::handle:vertical {{
            height: 2px;
        }}
        
        /* الإطارات */
        QFrame[frameShape="1"] {{
            border: 1px solid {theme['border']};
        }}
        
        QFrame[frameShape="2"] {{
            border: 1px solid {theme['border']};
        }}
        
        /* تخصيصات للتحميلات */
        QWidget[downloadStatus="downloading"] {{
            border-left: 4px solid {theme['download_active']};
        }}
        
        QWidget[downloadStatus="paused"] {{
            border-left: 4px solid {theme['download_paused']};
        }}
        
        QWidget[downloadStatus="completed"] {{
            border-left: 4px solid {theme['download_completed']};
        }}
        
        QWidget[downloadStatus="failed"] {{
            border-left: 4px solid {theme['download_failed']};
        }}
        
        QWidget[downloadStatus="waiting"] {{
            border-left: 4px solid {theme['download_waiting']};
        }}
        """
        
    def get_download_status_color(self, status: str) -> str:
        """الحصول على لون حالة التحميل"""
        theme = self.get_current_theme()
        status_colors = {
            "downloading": theme['download_active'],
            "paused": theme['download_paused'],
            "completed": theme['download_completed'],
            "failed": theme['download_failed'],
            "waiting": theme['download_waiting'],
            "cancelled": theme['text_disabled']
        }
        return status_colors.get(status, theme['text_secondary'])
        
    def get_available_themes(self) -> list:
        """الحصول على قائمة الثيمات المتاحة"""
        return list(self.themes.keys())
        
    def create_custom_theme(self, name: str, base_theme: str, overrides: Dict[str, str]):
        """إنشاء ثيم مخصص"""
        if base_theme in self.themes:
            custom_theme = self.themes[base_theme].copy()
            custom_theme.update(overrides)
            self.themes[name] = custom_theme
            return True
        return False