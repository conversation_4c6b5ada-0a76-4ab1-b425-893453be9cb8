#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional Download Manager
نقطة التشغيل الرئيسية للتطبيق
"""

import sys
import os
import asyncio
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, QDir
from PySide6.QtGui import QIcon

# إضافة مجلد المشروع إلى المسار
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.main_window import MainWindow
from core.simple_database import SimpleDatabaseManager

class DownloadManagerApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("Professional Download Manager")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("DownloadManager")
        
        # إعداد الأيقونة
        icon_path = project_root / "assets" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # إعداد الترجمة
        self.setup_translation()
        
        # إعداد قاعدة البيانات
        self.db_manager = SimpleDatabaseManager()
        
        # إنشاء النافذة الرئيسية
        self.main_window = MainWindow()
        
    def setup_translation(self):
        """إعداد نظام الترجمة"""
        self.translator = QTranslator()
        
        # تحديد اللغة الافتراضية (العربية)
        locale = QLocale.system().name()
        
        # مسار ملفات الترجمة
        translations_path = project_root / "assets" / "translations"
        
        # تحميل ملف الترجمة المناسب
        if self.translator.load(f"app_{locale}", str(translations_path)):
            self.app.installTranslator(self.translator)
        else:
            # تحميل الترجمة العربية كافتراضية
            if self.translator.load("app_ar", str(translations_path)):
                self.app.installTranslator(self.translator)
    
    def run(self):
        """تشغيل التطبيق"""
        self.main_window.show()
        return self.app.exec()

def main():
    """النقطة الرئيسية لتشغيل التطبيق"""
    try:
        # إنشاء مجلدات المشروع إذا لم تكن موجودة
        directories = [
            "assets/icons",
            "assets/translations", 
            "assets/themes",
            "downloads",
            "temp"
        ]
        
        for directory in directories:
            Path(project_root / directory).mkdir(parents=True, exist_ok=True)
        
        # تشغيل التطبيق
        app = DownloadManagerApp()
        sys.exit(app.run())
        
    except Exception as e:
        import traceback
        print(f"خطأ في تشغيل التطبيق: {e}")
        print("تفاصيل الخطأ:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()