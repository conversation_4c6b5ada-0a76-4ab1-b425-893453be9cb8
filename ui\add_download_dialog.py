#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حوار إضافة تحميل جديد
Add New Download Dialog
"""

import os
import asyncio
import aiohttp
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QPushButton, QLabel, QComboBox, QSpinBox, QCheckBox, QGroupBox,
    QFileDialog, QProgressBar, QTextEdit, QMessageBox, QFrame
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QIcon, QFont

from core.download_manager import DownloadInfo
from core.file_utils import FileUtils

class URLAnalyzer(QThread):
    """محلل الروابط"""
    
    analysis_completed = Signal(dict)  # معلومات الملف
    analysis_failed = Signal(str)  # رسالة الخطأ
    
    def __init__(self, url: str):
        super().__init__()
        self.url = url
        
    def run(self):
        """تشغيل تحليل الرابط"""
        try:
            asyncio.run(self.analyze_url())
        except Exception as e:
            self.analysis_failed.emit(str(e))
            
    async def analyze_url(self):
        """تحليل الرابط"""
        try:
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # طلب HEAD للحصول على معلومات الملف
                async with session.head(self.url) as response:
                    if response.status == 200:
                        info = self.extract_file_info(response)
                        self.analysis_completed.emit(info)
                    else:
                        # محاولة طلب GET مع تحديد النطاق
                        headers = {'Range': 'bytes=0-1023'}
                        async with session.get(self.url, headers=headers) as get_response:
                            info = self.extract_file_info(get_response)
                            self.analysis_completed.emit(info)
                            
        except Exception as e:
            self.analysis_failed.emit(f"فشل في تحليل الرابط: {str(e)}")
            
    def extract_file_info(self, response) -> dict:
        """استخراج معلومات الملف من الاستجابة"""
        info = {
            'filename': '',
            'file_size': 0,
            'mime_type': '',
            'resume_supported': False,
            'file_type': 'unknown'
        }
        
        # استخراج اسم الملف
        content_disposition = response.headers.get('Content-Disposition', '')
        if 'filename=' in content_disposition:
            filename = content_disposition.split('filename=')[1].strip('"\'')
            info['filename'] = filename
        else:
            info['filename'] = FileUtils.extract_filename_from_url(self.url)
            
        # حجم الملف
        content_length = response.headers.get('Content-Length')
        if content_length:
            info['file_size'] = int(content_length)
            
        # نوع MIME
        info['mime_type'] = response.headers.get('Content-Type', '')
        
        # دعم الاستئناف
        accept_ranges = response.headers.get('Accept-Ranges', '')
        info['resume_supported'] = 'bytes' in accept_ranges.lower()
        
        # تحديد نوع الملف
        if info['mime_type']:
            info['file_type'] = FileUtils.get_file_type_from_mime(info['mime_type'])
        else:
            info['file_type'] = FileUtils.get_file_type(info['filename'])
            
        return info

class AddDownloadDialog(QDialog):
    """حوار إضافة تحميل جديد"""
    
    def __init__(self, parent=None, url: str = ""):
        super().__init__(parent)
        self.url = url
        self.file_info = {}
        self.analyzer = None
        
        self.setup_ui()
        self.setup_connections()
        
        # إذا كان هناك رابط، تحليله تلقائياً
        if url:
            self.url_edit.setText(url)
            self.analyze_url()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة تحميل جديد")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # قسم الرابط
        self.create_url_section()
        layout.addWidget(self.url_group)
        
        # قسم معلومات الملف
        self.create_file_info_section()
        layout.addWidget(self.file_info_group)
        
        # قسم إعدادات التحميل
        self.create_download_settings_section()
        layout.addWidget(self.settings_group)
        
        # قسم الإعدادات المتقدمة
        self.create_advanced_settings_section()
        layout.addWidget(self.advanced_group)
        
        # أزرار الحوار
        self.create_dialog_buttons()
        layout.addWidget(self.buttons_frame)
        
    def create_url_section(self):
        """إنشاء قسم الرابط"""
        self.url_group = QGroupBox("رابط التحميل")
        url_layout = QVBoxLayout(self.url_group)
        
        # حقل الرابط
        url_input_layout = QHBoxLayout()
        
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("أدخل رابط الملف المراد تحميله...")
        self.url_edit.setMinimumHeight(35)
        
        self.analyze_btn = QPushButton("تحليل")
        self.analyze_btn.setFixedSize(80, 35)
        self.analyze_btn.clicked.connect(self.analyze_url)
        
        url_input_layout.addWidget(self.url_edit)
        url_input_layout.addWidget(self.analyze_btn)
        
        # شريط تقدم التحليل
        self.analysis_progress = QProgressBar()
        self.analysis_progress.setVisible(False)
        self.analysis_progress.setRange(0, 0)  # شريط تقدم غير محدد
        
        # رسالة حالة التحليل
        self.analysis_status = QLabel()
        self.analysis_status.setStyleSheet("color: #666666; font-size: 9pt;")
        
        url_layout.addLayout(url_input_layout)
        url_layout.addWidget(self.analysis_progress)
        url_layout.addWidget(self.analysis_status)
        
    def create_file_info_section(self):
        """إنشاء قسم معلومات الملف"""
        self.file_info_group = QGroupBox("معلومات الملف")
        info_layout = QFormLayout(self.file_info_group)
        
        # اسم الملف
        self.filename_edit = QLineEdit()
        self.filename_edit.setMinimumHeight(30)
        info_layout.addRow("اسم الملف:", self.filename_edit)
        
        # حجم الملف
        self.filesize_label = QLabel("غير معروف")
        self.filesize_label.setStyleSheet("color: #666666;")
        info_layout.addRow("حجم الملف:", self.filesize_label)
        
        # نوع الملف
        self.filetype_label = QLabel("غير معروف")
        self.filetype_label.setStyleSheet("color: #666666;")
        info_layout.addRow("نوع الملف:", self.filetype_label)
        
        # دعم الاستئناف
        self.resume_support_label = QLabel("غير معروف")
        self.resume_support_label.setStyleSheet("color: #666666;")
        info_layout.addRow("دعم الاستئناف:", self.resume_support_label)
        
    def create_download_settings_section(self):
        """إنشاء قسم إعدادات التحميل"""
        self.settings_group = QGroupBox("إعدادات التحميل")
        settings_layout = QFormLayout(self.settings_group)
        
        # مجلد الحفظ
        save_layout = QHBoxLayout()
        
        self.save_path_edit = QLineEdit()
        self.save_path_edit.setText(str(Path.home() / "Downloads"))
        self.save_path_edit.setMinimumHeight(30)
        
        self.browse_btn = QPushButton("تصفح")
        self.browse_btn.setFixedSize(80, 30)
        self.browse_btn.clicked.connect(self.browse_save_path)
        
        save_layout.addWidget(self.save_path_edit)
        save_layout.addWidget(self.browse_btn)
        
        settings_layout.addRow("مجلد الحفظ:", save_layout)
        
        # الأولوية
        self.priority_combo = QComboBox()
        self.priority_combo.addItems([
            "منخفضة جداً (1)",
            "منخفضة (2)", 
            "متوسطة (3)",
            "عالية (4)",
            "عالية جداً (5)"
        ])
        self.priority_combo.setCurrentIndex(2)  # متوسطة افتراضياً
        self.priority_combo.setMinimumHeight(30)
        
        settings_layout.addRow("الأولوية:", self.priority_combo)
        
        # بدء التحميل فوراً
        self.start_immediately_check = QCheckBox("بدء التحميل فوراً")
        self.start_immediately_check.setChecked(True)
        
        settings_layout.addRow("", self.start_immediately_check)
        
    def create_advanced_settings_section(self):
        """إنشاء قسم الإعدادات المتقدمة"""
        self.advanced_group = QGroupBox("إعدادات متقدمة")
        self.advanced_group.setCheckable(True)
        self.advanced_group.setChecked(False)
        
        advanced_layout = QFormLayout(self.advanced_group)
        
        # عدد الاتصالات المتوازية
        self.connections_spin = QSpinBox()
        self.connections_spin.setRange(1, 16)
        self.connections_spin.setValue(4)
        self.connections_spin.setMinimumHeight(30)
        
        advanced_layout.addRow("عدد الاتصالات:", self.connections_spin)
        
        # User Agent مخصص
        self.user_agent_edit = QLineEdit()
        self.user_agent_edit.setPlaceholderText("اتركه فارغاً للاستخدام الافتراضي")
        self.user_agent_edit.setMinimumHeight(30)
        
        advanced_layout.addRow("User Agent:", self.user_agent_edit)
        
        # Headers إضافية
        self.headers_edit = QTextEdit()
        self.headers_edit.setPlaceholderText("أدخل headers إضافية (كل header في سطر منفصل)\nمثال: Authorization: Bearer token")
        self.headers_edit.setMaximumHeight(80)
        
        advanced_layout.addRow("Headers إضافية:", self.headers_edit)
        
        # إعادة المحاولة
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(0, 10)
        self.retry_spin.setValue(3)
        self.retry_spin.setMinimumHeight(30)
        
        advanced_layout.addRow("عدد المحاولات:", self.retry_spin)
        
    def create_dialog_buttons(self):
        """إنشاء أزرار الحوار"""
        self.buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(self.buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setFixedSize(100, 35)
        self.cancel_btn.clicked.connect(self.reject)
        
        # زر الموافقة
        self.ok_btn = QPushButton("إضافة التحميل")
        self.ok_btn.setFixedSize(120, 35)
        self.ok_btn.setDefault(True)
        self.ok_btn.clicked.connect(self.accept_download)
        self.ok_btn.setEnabled(False)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.ok_btn)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.url_edit.textChanged.connect(self.on_url_changed)
        self.filename_edit.textChanged.connect(self.validate_input)
        self.save_path_edit.textChanged.connect(self.validate_input)
        
    def on_url_changed(self):
        """عند تغيير الرابط"""
        self.ok_btn.setEnabled(False)
        self.clear_file_info()
        
    def analyze_url(self):
        """تحليل الرابط"""
        url = self.url_edit.text().strip()
        
        if not url:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رابط صحيح")
            return
            
        if not FileUtils.is_valid_url(url):
            QMessageBox.warning(self, "تحذير", "الرابط المدخل غير صحيح")
            return
            
        # بدء التحليل
        self.analysis_progress.setVisible(True)
        self.analysis_status.setText("جاري تحليل الرابط...")
        self.analyze_btn.setEnabled(False)
        
        # إنشاء محلل الرابط
        self.analyzer = URLAnalyzer(url)
        self.analyzer.analysis_completed.connect(self.on_analysis_completed)
        self.analyzer.analysis_failed.connect(self.on_analysis_failed)
        self.analyzer.start()
        
        # تايمر للمهلة الزمنية
        self.analysis_timer = QTimer()
        self.analysis_timer.timeout.connect(self.on_analysis_timeout)
        self.analysis_timer.start(15000)  # 15 ثانية
        
    def on_analysis_completed(self, info: dict):
        """عند اكتمال التحليل"""
        self.analysis_timer.stop()
        self.analysis_progress.setVisible(False)
        self.analyze_btn.setEnabled(True)
        
        self.file_info = info
        self.update_file_info_display()
        self.analysis_status.setText("تم تحليل الرابط بنجاح")
        self.analysis_status.setStyleSheet("color: #4CAF50; font-size: 9pt;")
        
        self.validate_input()
        
    def on_analysis_failed(self, error: str):
        """عند فشل التحليل"""
        self.analysis_timer.stop()
        self.analysis_progress.setVisible(False)
        self.analyze_btn.setEnabled(True)
        
        self.analysis_status.setText(f"فشل التحليل: {error}")
        self.analysis_status.setStyleSheet("color: #F44336; font-size: 9pt;")
        
        # تعيين اسم ملف افتراضي
        if not self.filename_edit.text():
            filename = FileUtils.extract_filename_from_url(self.url_edit.text())
            self.filename_edit.setText(filename)
            
        self.validate_input()
        
    def on_analysis_timeout(self):
        """عند انتهاء المهلة الزمنية للتحليل"""
        if self.analyzer:
            self.analyzer.terminate()
            
        self.analysis_timer.stop()
        self.analysis_progress.setVisible(False)
        self.analyze_btn.setEnabled(True)
        
        self.analysis_status.setText("انتهت المهلة الزمنية للتحليل")
        self.analysis_status.setStyleSheet("color: #FF9800; font-size: 9pt;")
        
        # تعيين اسم ملف افتراضي
        if not self.filename_edit.text():
            filename = FileUtils.extract_filename_from_url(self.url_edit.text())
            self.filename_edit.setText(filename)
            
        self.validate_input()
        
    def update_file_info_display(self):
        """تحديث عرض معلومات الملف"""
        if not self.file_info:
            return
            
        # اسم الملف
        if self.file_info.get('filename') and not self.filename_edit.text():
            self.filename_edit.setText(self.file_info['filename'])
            
        # حجم الملف
        file_size = self.file_info.get('file_size', 0)
        if file_size > 0:
            self.filesize_label.setText(FileUtils.format_file_size(file_size))
        else:
            self.filesize_label.setText("غير معروف")
            
        # نوع الملف
        file_type = self.file_info.get('file_type', 'unknown')
        file_info = FileUtils.get_file_info(self.filename_edit.text())
        self.filetype_label.setText(file_info['description'])
        
        # دعم الاستئناف
        resume_supported = self.file_info.get('resume_supported', False)
        if resume_supported:
            self.resume_support_label.setText("مدعوم")
            self.resume_support_label.setStyleSheet("color: #4CAF50;")
        else:
            self.resume_support_label.setText("غير مدعوم")
            self.resume_support_label.setStyleSheet("color: #FF9800;")
            
    def clear_file_info(self):
        """مسح معلومات الملف"""
        self.file_info = {}
        self.filename_edit.clear()
        self.filesize_label.setText("غير معروف")
        self.filetype_label.setText("غير معروف")
        self.resume_support_label.setText("غير معروف")
        self.analysis_status.clear()
        
    def browse_save_path(self):
        """تصفح مجلد الحفظ"""
        folder = QFileDialog.getExistingDirectory(
            self, "اختر مجلد الحفظ", self.save_path_edit.text()
        )
        
        if folder:
            self.save_path_edit.setText(folder)
            
    def validate_input(self):
        """التحقق من صحة المدخلات"""
        url = self.url_edit.text().strip()
        filename = self.filename_edit.text().strip()
        save_path = self.save_path_edit.text().strip()
        
        valid = (
            FileUtils.is_valid_url(url) and
            filename and
            save_path and
            os.path.exists(save_path)
        )
        
        self.ok_btn.setEnabled(valid)
        
    def accept_download(self):
        """قبول إضافة التحميل"""
        if not self.validate_download_info():
            return
            
        self.accept()
        
    def validate_download_info(self) -> bool:
        """التحقق من معلومات التحميل"""
        # التحقق من الرابط
        url = self.url_edit.text().strip()
        if not FileUtils.is_valid_url(url):
            QMessageBox.warning(self, "خطأ", "الرابط المدخل غير صحيح")
            return False
            
        # التحقق من اسم الملف
        filename = self.filename_edit.text().strip()
        if not filename:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الملف")
            return False
            
        # التحقق من مجلد الحفظ
        save_path = self.save_path_edit.text().strip()
        if not os.path.exists(save_path):
            QMessageBox.warning(self, "خطأ", "مجلد الحفظ غير موجود")
            return False
            
        # التحقق من وجود الملف
        full_path = os.path.join(save_path, filename)
        if os.path.exists(full_path):
            reply = QMessageBox.question(
                self, "تأكيد الاستبدال",
                f"الملف '{filename}' موجود بالفعل.\nهل تريد استبداله؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                return False
                
        return True
        
    def get_download_info(self) -> DownloadInfo:
        """الحصول على معلومات التحميل"""
        url = self.url_edit.text().strip()
        filename = FileUtils.sanitize_filename(self.filename_edit.text().strip())
        save_path = self.save_path_edit.text().strip()
        
        # إنشاء المسار الكامل
        full_path = FileUtils.get_unique_filename(save_path, filename)
        
        # الأولوية
        priority = self.priority_combo.currentIndex() + 1
        
        # إنشاء معلومات التحميل
        download_info = DownloadInfo(
            url=url,
            filename=os.path.basename(full_path),
            save_path=full_path,
            priority=priority
        )
        
        # إضافة معلومات إضافية من التحليل
        if self.file_info:
            download_info.total_size = self.file_info.get('file_size', 0)
            download_info.resume_supported = self.file_info.get('resume_supported', False)
            download_info.file_type = self.file_info.get('file_type', 'unknown')
            
        return download_info
        
    def get_advanced_settings(self) -> dict:
        """الحصول على الإعدادات المتقدمة"""
        if not self.advanced_group.isChecked():
            return {}
            
        settings = {
            'max_connections': self.connections_spin.value(),
            'retry_count': self.retry_spin.value()
        }
        
        # User Agent مخصص
        user_agent = self.user_agent_edit.text().strip()
        if user_agent:
            settings['user_agent'] = user_agent
            
        # Headers إضافية
        headers_text = self.headers_edit.toPlainText().strip()
        if headers_text:
            headers = {}
            for line in headers_text.split('\n'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    headers[key.strip()] = value.strip()
            settings['headers'] = headers
            
        return settings