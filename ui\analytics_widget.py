#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة التحليل البياني والإحصائيات
Analytics and Statistics Widget
"""

import time
from datetime import datetime, timedelta
from typing import List, Dict
import pyqtgraph as pg
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QComboBox, QPushButton, QFrame, QGroupBox, QGridLayout,
    QSplitter, QScrollArea, QDateEdit, QSpinBox
)
from PySide6.QtCore import Qt, QTimer, QDate, Signal
from PySide6.QtGui import QFont, QColor

from core.database import DatabaseManager
from core.file_utils import FileUtils

class AnalyticsWidget(QWidget):
    """واجهة التحليل البياني"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        
        # إعداد pyqtgraph
        pg.setConfigOptions(antialias=True)
        pg.setConfigOption('background', 'w')
        pg.setConfigOption('foreground', 'k')
        
        self.setup_ui()
        self.setup_refresh_timer()
        self.refresh_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط التحكم العلوي
        self.create_control_bar()
        layout.addWidget(self.control_frame)
        
        # التبويبات الرئيسية
        self.create_main_tabs()
        layout.addWidget(self.main_tabs)
        
    def create_control_bar(self):
        """إنشاء شريط التحكم"""
        self.control_frame = QFrame()
        self.control_frame.setFixedHeight(50)
        self.control_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
            }
        """)
        
        control_layout = QHBoxLayout(self.control_frame)
        control_layout.setContentsMargins(15, 5, 15, 5)
        
        # فترة التحليل
        control_layout.addWidget(QLabel("فترة التحليل:"))
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "آخر 24 ساعة",
            "آخر أسبوع", 
            "آخر شهر",
            "آخر 3 أشهر",
            "آخر سنة",
            "الكل"
        ])
        self.period_combo.setCurrentIndex(2)  # آخر شهر
        self.period_combo.currentTextChanged.connect(self.refresh_data)
        control_layout.addWidget(self.period_combo)
        
        control_layout.addStretch()
        
        # زر التحديث
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setFixedSize(80, 30)
        self.refresh_btn.clicked.connect(self.refresh_data)
        control_layout.addWidget(self.refresh_btn)
        
        # زر التصدير
        self.export_btn = QPushButton("تصدير")
        self.export_btn.setFixedSize(80, 30)
        self.export_btn.clicked.connect(self.export_analytics)
        control_layout.addWidget(self.export_btn)
        
    def create_main_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        self.main_tabs = QTabWidget()
        
        # تبويب الإحصائيات العامة
        self.create_overview_tab()
        self.main_tabs.addTab(self.overview_widget, "نظرة عامة")
        
        # تبويب الرسوم البيانية اليومية
        self.create_daily_charts_tab()
        self.main_tabs.addTab(self.daily_widget, "الإحصائيات اليومية")
        
        # تبويب الرسوم البيانية الشهرية
        self.create_monthly_charts_tab()
        self.main_tabs.addTab(self.monthly_widget, "الإحصائيات الشهرية")
        
        # تبويب أنواع الملفات
        self.create_file_types_tab()
        self.main_tabs.addTab(self.file_types_widget, "أنواع الملفات")
        
        # تبويب السرعة اللحظية
        self.create_speed_tab()
        self.main_tabs.addTab(self.speed_widget, "السرعة اللحظية")
        
    def create_overview_tab(self):
        """إنشاء تبويب النظرة العامة"""
        self.overview_widget = QWidget()
        layout = QVBoxLayout(self.overview_widget)
        layout.setSpacing(15)
        
        # بطاقات الإحصائيات
        self.create_stats_cards()
        layout.addWidget(self.stats_frame)
        
        # الرسم البياني الدائري لأنواع الملفات
        self.create_pie_chart()
        layout.addWidget(self.pie_chart_frame)
        
    def create_stats_cards(self):
        """إنشاء بطاقات الإحصائيات"""
        self.stats_frame = QFrame()
        stats_layout = QGridLayout(self.stats_frame)
        stats_layout.setSpacing(15)
        
        # بطاقة إجمالي التحميلات
        self.total_downloads_card = self.create_stat_card(
            "إجمالي التحميلات", "0", "#2196F3"
        )
        stats_layout.addWidget(self.total_downloads_card, 0, 0)
        
        # بطاقة التحميلات المكتملة
        self.completed_downloads_card = self.create_stat_card(
            "التحميلات المكتملة", "0", "#4CAF50"
        )
        stats_layout.addWidget(self.completed_downloads_card, 0, 1)
        
        # بطاقة التحميلات الفاشلة
        self.failed_downloads_card = self.create_stat_card(
            "التحميلات الفاشلة", "0", "#F44336"
        )
        stats_layout.addWidget(self.failed_downloads_card, 0, 2)
        
        # بطاقة معدل النجاح
        self.success_rate_card = self.create_stat_card(
            "معدل النجاح", "0%", "#FF9800"
        )
        stats_layout.addWidget(self.success_rate_card, 0, 3)
        
        # بطاقة إجمالي البيانات
        self.total_data_card = self.create_stat_card(
            "إجمالي البيانات", "0 B", "#9C27B0"
        )
        stats_layout.addWidget(self.total_data_card, 1, 0)
        
        # بطاقة متوسط السرعة
        self.avg_speed_card = self.create_stat_card(
            "متوسط السرعة", "0 KB/s", "#00BCD4"
        )
        stats_layout.addWidget(self.avg_speed_card, 1, 1)
        
        # بطاقة أقصى سرعة
        self.max_speed_card = self.create_stat_card(
            "أقصى سرعة", "0 KB/s", "#795548"
        )
        stats_layout.addWidget(self.max_speed_card, 1, 2)
        
        # بطاقة متوسط حجم الملف
        self.avg_file_size_card = self.create_stat_card(
            "متوسط حجم الملف", "0 B", "#607D8B"
        )
        stats_layout.addWidget(self.avg_file_size_card, 1, 3)
        
    def create_stat_card(self, title: str, value: str, color: str) -> QFrame:
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFixedHeight(100)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                border-left: 4px solid {color};
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 9))
        title_label.setStyleSheet("color: #666666;")
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addStretch()
        
        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
        
    def create_pie_chart(self):
        """إنشاء الرسم البياني الدائري"""
        self.pie_chart_frame = QGroupBox("توزيع أنواع الملفات")
        layout = QVBoxLayout(self.pie_chart_frame)
        
        # إنشاء الرسم البياني
        self.pie_plot = pg.PlotWidget()
        self.pie_plot.setFixedHeight(300)
        self.pie_plot.hideAxis('left')
        self.pie_plot.hideAxis('bottom')
        self.pie_plot.setAspectLocked(True)
        
        layout.addWidget(self.pie_plot)
        
    def create_daily_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية اليومية"""
        self.daily_widget = QWidget()
        layout = QVBoxLayout(self.daily_widget)
        
        # مقسم للرسوم البيانية
        splitter = QSplitter(Qt.Vertical)
        
        # رسم بياني لعدد التحميلات اليومية
        self.daily_downloads_chart = self.create_chart(
            "عدد التحميلات اليومية", "التاريخ", "عدد التحميلات"
        )
        splitter.addWidget(self.daily_downloads_chart)
        
        # رسم بياني للبيانات المحملة يومياً
        self.daily_data_chart = self.create_chart(
            "البيانات المحملة يومياً", "التاريخ", "البيانات (MB)"
        )
        splitter.addWidget(self.daily_data_chart)
        
        # رسم بياني لمتوسط السرعة اليومية
        self.daily_speed_chart = self.create_chart(
            "متوسط السرعة اليومية", "التاريخ", "السرعة (KB/s)"
        )
        splitter.addWidget(self.daily_speed_chart)
        
        layout.addWidget(splitter)
        
    def create_monthly_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية الشهرية"""
        self.monthly_widget = QWidget()
        layout = QVBoxLayout(self.monthly_widget)
        
        # مقسم للرسوم البيانية
        splitter = QSplitter(Qt.Vertical)
        
        # رسم بياني لعدد التحميلات الشهرية
        self.monthly_downloads_chart = self.create_chart(
            "عدد التحميلات الشهرية", "الشهر", "عدد التحميلات"
        )
        splitter.addWidget(self.monthly_downloads_chart)
        
        # رسم بياني للبيانات المحملة شهرياً
        self.monthly_data_chart = self.create_chart(
            "البيانات المحملة شهرياً", "الشهر", "البيانات (GB)"
        )
        splitter.addWidget(self.monthly_data_chart)
        
        layout.addWidget(splitter)
        
    def create_file_types_tab(self):
        """إنشاء تبويب أنواع الملفات"""
        self.file_types_widget = QWidget()
        layout = QVBoxLayout(self.file_types_widget)
        
        # رسم بياني شريطي لأنواع الملفات
        self.file_types_chart = self.create_chart(
            "إحصائيات أنواع الملفات", "نوع الملف", "عدد التحميلات"
        )
        layout.addWidget(self.file_types_chart)
        
    def create_speed_tab(self):
        """إنشاء تبويب السرعة اللحظية"""
        self.speed_widget = QWidget()
        layout = QVBoxLayout(self.speed_widget)
        
        # رسم بياني للسرعة اللحظية
        self.speed_chart = self.create_chart(
            "السرعة اللحظية", "الوقت", "السرعة (KB/s)"
        )
        self.speed_chart.setLabel('bottom', 'الوقت')
        self.speed_chart.setLabel('left', 'السرعة (KB/s)')
        
        # إعداد الرسم البياني للتحديث المباشر
        self.speed_data_x = []
        self.speed_data_y = []
        self.speed_curve = self.speed_chart.plot(
            self.speed_data_x, self.speed_data_y,
            pen=pg.mkPen(color='#2196F3', width=2)
        )
        
        layout.addWidget(self.speed_chart)
        
    def create_chart(self, title: str, x_label: str, y_label: str) -> pg.PlotWidget:
        """إنشاء رسم بياني"""
        chart = pg.PlotWidget()
        chart.setLabel('bottom', x_label)
        chart.setLabel('left', y_label)
        chart.setTitle(title)
        chart.showGrid(x=True, y=True)
        chart.setBackground('w')
        
        return chart
        
    def setup_refresh_timer(self):
        """إعداد تايمر التحديث"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.update_speed_chart)
        self.refresh_timer.start(1000)  # تحديث كل ثانية
        
    def refresh_data(self):
        """تحديث جميع البيانات"""
        try:
            self.update_overview_stats()
            self.update_daily_charts()
            self.update_monthly_charts()
            self.update_file_types_chart()
            self.update_pie_chart()
            
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            
    def get_period_days(self) -> int:
        """الحصول على عدد الأيام للفترة المحددة"""
        period_map = {
            "آخر 24 ساعة": 1,
            "آخر أسبوع": 7,
            "آخر شهر": 30,
            "آخر 3 أشهر": 90,
            "آخر سنة": 365,
            "الكل": 0
        }
        
        selected_period = self.period_combo.currentText()
        return period_map.get(selected_period, 30)
        
    def update_overview_stats(self):
        """تحديث إحصائيات النظرة العامة"""
        stats = self.db_manager.get_total_statistics()
        
        if stats:
            # تحديث البطاقات
            self.total_downloads_card.value_label.setText(str(stats.get('total_downloads', 0)))
            self.completed_downloads_card.value_label.setText(str(stats.get('completed_downloads', 0)))
            self.failed_downloads_card.value_label.setText(str(stats.get('failed_downloads', 0)))
            self.success_rate_card.value_label.setText(f"{stats.get('success_rate', 0):.1f}%")
            self.total_data_card.value_label.setText(FileUtils.format_file_size(stats.get('total_bytes', 0)))
            self.avg_speed_card.value_label.setText(FileUtils.format_speed(stats.get('avg_speed', 0)))
            self.max_speed_card.value_label.setText(FileUtils.format_speed(stats.get('max_speed', 0)))
            
            # حساب متوسط حجم الملف
            if stats.get('completed_downloads', 0) > 0:
                avg_file_size = stats.get('total_bytes', 0) / stats.get('completed_downloads', 1)
                self.avg_file_size_card.value_label.setText(FileUtils.format_file_size(avg_file_size))
            else:
                self.avg_file_size_card.value_label.setText("0 B")
                
    def update_daily_charts(self):
        """تحديث الرسوم البيانية اليومية"""
        days = self.get_period_days()
        daily_stats = self.db_manager.get_daily_stats(days if days > 0 else 365)
        
        if not daily_stats:
            return
            
        # تحضير البيانات
        dates = []
        downloads_count = []
        data_amounts = []
        avg_speeds = []
        
        for stat in reversed(daily_stats):  # عكس الترتيب للحصول على التسلسل الزمني الصحيح
            dates.append(stat['date'])
            downloads_count.append(stat.get('completed_downloads', 0))
            data_amounts.append(stat.get('total_bytes', 0) / (1024 * 1024))  # تحويل إلى MB
            avg_speeds.append(stat.get('avg_speed', 0) / 1024)  # تحويل إلى KB/s
            
        # تحديث الرسوم البيانية
        x_data = list(range(len(dates)))
        
        # رسم عدد التحميلات
        self.daily_downloads_chart.clear()
        self.daily_downloads_chart.plot(
            x_data, downloads_count,
            pen=pg.mkPen(color='#2196F3', width=2),
            symbol='o', symbolBrush='#2196F3'
        )
        
        # رسم البيانات المحملة
        self.daily_data_chart.clear()
        self.daily_data_chart.plot(
            x_data, data_amounts,
            pen=pg.mkPen(color='#4CAF50', width=2),
            symbol='s', symbolBrush='#4CAF50'
        )
        
        # رسم متوسط السرعة
        self.daily_speed_chart.clear()
        self.daily_speed_chart.plot(
            x_data, avg_speeds,
            pen=pg.mkPen(color='#FF9800', width=2),
            symbol='^', symbolBrush='#FF9800'
        )
        
    def update_monthly_charts(self):
        """تحديث الرسوم البيانية الشهرية"""
        monthly_stats = self.db_manager.get_monthly_stats(12)
        
        if not monthly_stats:
            return
            
        # تحضير البيانات
        months = []
        downloads_count = []
        data_amounts = []
        
        for stat in reversed(monthly_stats):
            months.append(stat['month'])
            downloads_count.append(stat.get('completed_downloads', 0))
            data_amounts.append(stat.get('total_bytes', 0) / (1024 * 1024 * 1024))  # تحويل إلى GB
            
        # تحديث الرسوم البيانية
        x_data = list(range(len(months)))
        
        # رسم عدد التحميلات الشهرية
        self.monthly_downloads_chart.clear()
        self.monthly_downloads_chart.plot(
            x_data, downloads_count,
            pen=pg.mkPen(color='#9C27B0', width=3),
            symbol='o', symbolBrush='#9C27B0', symbolSize=8
        )
        
        # رسم البيانات المحملة شهرياً
        self.monthly_data_chart.clear()
        self.monthly_data_chart.plot(
            x_data, data_amounts,
            pen=pg.mkPen(color='#00BCD4', width=3),
            symbol='s', symbolBrush='#00BCD4', symbolSize=8
        )
        
    def update_file_types_chart(self):
        """تحديث رسم أنواع الملفات"""
        file_type_stats = self.db_manager.get_file_type_stats()
        
        if not file_type_stats:
            return
            
        # تحضير البيانات
        file_types = []
        counts = []
        colors = ['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0', '#00BCD4', '#795548']
        
        for i, stat in enumerate(file_type_stats[:7]):  # أول 7 أنواع فقط
            file_info = FileUtils.get_file_info(f"dummy.{stat['file_type']}")
            file_types.append(file_info['description'])
            counts.append(stat['count'])
            
        # إنشاء رسم شريطي
        self.file_types_chart.clear()
        
        x_data = list(range(len(file_types)))
        bars = pg.BarGraphItem(
            x=x_data, height=counts, width=0.6,
            brushes=[colors[i % len(colors)] for i in range(len(counts))]
        )
        
        self.file_types_chart.addItem(bars)
        
        # تعيين تسميات المحاور
        x_dict = dict(enumerate(file_types))
        x_axis = self.file_types_chart.getAxis('bottom')
        x_axis.setTicks([list(x_dict.items())])
        
    def update_pie_chart(self):
        """تحديث الرسم البياني الدائري"""
        file_type_stats = self.db_manager.get_file_type_stats()
        
        if not file_type_stats:
            return
            
        self.pie_plot.clear()
        
        # تحضير البيانات
        total_count = sum(stat['count'] for stat in file_type_stats)
        if total_count == 0:
            return
            
        colors = ['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0', '#00BCD4', '#795548']
        angle_start = 0
        
        for i, stat in enumerate(file_type_stats[:7]):
            count = stat['count']
            percentage = count / total_count
            angle_span = percentage * 360
            
            # رسم القطاع
            angles = []
            x_coords = []
            y_coords = []
            
            for angle in range(int(angle_start), int(angle_start + angle_span) + 1):
                rad = angle * 3.14159 / 180
                x = 0.8 * pg.np.cos(rad)
                y = 0.8 * pg.np.sin(rad)
                angles.append(angle)
                x_coords.append(x)
                y_coords.append(y)
                
            # إضافة نقطة المركز
            x_coords.insert(0, 0)
            y_coords.insert(0, 0)
            x_coords.append(0)
            y_coords.append(0)
            
            # رسم القطاع
            brush = pg.mkBrush(colors[i % len(colors)])
            self.pie_plot.plot(
                x_coords, y_coords,
                fillLevel=0, brush=brush, pen=pg.mkPen('w', width=2)
            )
            
            angle_start += angle_span
            
    def update_speed_chart(self):
        """تحديث رسم السرعة اللحظية"""
        # هذه الدالة ستحصل على السرعة الحالية من مدير التحميلات
        # وتضيفها إلى الرسم البياني
        
        current_time = time.time()
        # current_speed = self.download_manager.get_total_speed()  # يجب ربطها بمدير التحميلات
        current_speed = 0  # مؤقتاً
        
        # إضافة النقطة الجديدة
        self.speed_data_x.append(current_time)
        self.speed_data_y.append(current_speed / 1024)  # تحويل إلى KB/s
        
        # الاحتفاظ بآخر 60 نقطة فقط (دقيقة واحدة)
        if len(self.speed_data_x) > 60:
            self.speed_data_x.pop(0)
            self.speed_data_y.pop(0)
            
        # تحديث الرسم البياني
        if len(self.speed_data_x) > 1:
            # تحويل الوقت إلى ثوانٍ نسبية
            relative_times = [(t - self.speed_data_x[0]) for t in self.speed_data_x]
            self.speed_curve.setData(relative_times, self.speed_data_y)
            
    def export_analytics(self):
        """تصدير التحليلات"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير التحليلات",
            f"analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                success = self.db_manager.export_data(file_path)
                if success:
                    QMessageBox.information(self, "تم", f"تم تصدير التحليلات إلى: {file_path}")
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تصدير التحليلات")
                    
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في التصدير: {str(e)}")
                
    def get_analytics_summary(self) -> Dict:
        """الحصول على ملخص التحليلات"""
        return {
            'total_stats': self.db_manager.get_total_statistics(),
            'daily_stats': self.db_manager.get_daily_stats(30),
            'monthly_stats': self.db_manager.get_monthly_stats(12),
            'file_type_stats': self.db_manager.get_file_type_stats()
        }