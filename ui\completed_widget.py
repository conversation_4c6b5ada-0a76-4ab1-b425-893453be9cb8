#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة التحميلات المكتملة
Completed Downloads Widget
"""

import os
import subprocess
from pathlib import Path
from typing import List, Dict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QLineEdit, QComboBox, QHeaderView, QMenu,
    QMessageBox, QFileDialog, QFrame, QGroupBox, QSplitter
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QIcon, QFont, QAction

from core.database import DatabaseManager
from core.file_utils import FileUtils

class CompletedWidget(QWidget):
    """واجهة التحميلات المكتملة"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.completed_downloads = []
        self.filtered_downloads = []
        
        self.setup_ui()
        self.load_completed_downloads()
        self.setup_refresh_timer()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar_frame)
        
        # جدول التحميلات المكتملة
        self.create_downloads_table()
        layout.addWidget(self.table_widget)
        
        # شريط الإحصائيات السفلي
        self.create_stats_bar()
        layout.addWidget(self.stats_frame)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFixedHeight(50)
        self.toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)
        
        # مربع البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في التحميلات المكتملة...")
        self.search_edit.setFixedHeight(30)
        self.search_edit.textChanged.connect(self.filter_downloads)
        
        # فلتر نوع الملف
        self.file_type_combo = QComboBox()
        self.file_type_combo.setFixedHeight(30)
        self.file_type_combo.addItem("جميع الأنواع", "all")
        self.populate_file_type_filter()
        self.file_type_combo.currentTextChanged.connect(self.filter_downloads)
        
        # زر التحديث
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setIcon(QIcon("assets/icons/refresh.png"))
        self.refresh_btn.setFixedSize(80, 30)
        self.refresh_btn.clicked.connect(self.load_completed_downloads)
        
        # زر فتح مجلد التحميلات
        self.open_folder_btn = QPushButton("فتح مجلد التحميلات")
        self.open_folder_btn.setIcon(QIcon("assets/icons/folder.png"))
        self.open_folder_btn.setFixedSize(150, 30)
        self.open_folder_btn.clicked.connect(self.open_downloads_folder)
        
        # زر مسح السجل
        self.clear_btn = QPushButton("مسح السجل")
        self.clear_btn.setIcon(QIcon("assets/icons/clear.png"))
        self.clear_btn.setFixedSize(80, 30)
        self.clear_btn.clicked.connect(self.clear_completed_downloads)
        
        # إضافة العناصر
        toolbar_layout.addWidget(QLabel("البحث:"))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(QLabel("النوع:"))
        toolbar_layout.addWidget(self.file_type_combo)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.open_folder_btn)
        toolbar_layout.addWidget(self.clear_btn)
        
    def create_downloads_table(self):
        """إنشاء جدول التحميلات"""
        self.table_widget = QTableWidget()
        
        # تعيين الأعمدة
        columns = [
            "اسم الملف",
            "الحجم", 
            "النوع",
            "تاريخ الإكمال",
            "السرعة المتوسطة",
            "المسار",
            "الحالة"
        ]
        
        self.table_widget.setColumnCount(len(columns))
        self.table_widget.setHorizontalHeaderLabels(columns)
        
        # إعدادات الجدول
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setSelectionBehavior(QTableWidget.SelectRows)
        self.table_widget.setSelectionMode(QTableWidget.ExtendedSelection)
        self.table_widget.setSortingEnabled(True)
        
        # تعيين عرض الأعمدة
        header = self.table_widget.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # اسم الملف
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الحجم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # السرعة
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # المسار
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة
        
        # القائمة المنبثقة
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.show_context_menu)
        
        # النقر المزدوج
        self.table_widget.itemDoubleClicked.connect(self.open_selected_file)
        
    def create_stats_bar(self):
        """إنشاء شريط الإحصائيات"""
        self.stats_frame = QFrame()
        self.stats_frame.setFixedHeight(40)
        self.stats_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
            }
        """)
        
        stats_layout = QHBoxLayout(self.stats_frame)
        stats_layout.setContentsMargins(15, 5, 15, 5)
        
        # إجمالي التحميلات
        self.total_downloads_label = QLabel("إجمالي التحميلات: 0")
        self.total_downloads_label.setFont(QFont("Arial", 9, QFont.Bold))
        
        # إجمالي الحجم
        self.total_size_label = QLabel("إجمالي الحجم: 0 B")
        
        # متوسط السرعة
        self.avg_speed_label = QLabel("متوسط السرعة: 0 KB/s")
        
        # آخر تحديث
        self.last_update_label = QLabel("آخر تحديث: --")
        
        stats_layout.addWidget(self.total_downloads_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.total_size_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.avg_speed_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.last_update_label)
        
    def setup_refresh_timer(self):
        """إعداد تايمر التحديث"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_completed_downloads)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية
        
    def populate_file_type_filter(self):
        """ملء فلتر نوع الملف"""
        file_types = self.db_manager.get_file_type_stats()
        
        for file_type_info in file_types:
            file_type = file_type_info['file_type']
            count = file_type_info['count']
            
            # الحصول على وصف نوع الملف
            type_info = FileUtils.get_file_info(f"dummy.{file_type}")
            description = type_info['description']
            
            self.file_type_combo.addItem(f"{description} ({count})", file_type)
            
    def load_completed_downloads(self):
        """تحميل التحميلات المكتملة"""
        try:
            self.completed_downloads = self.db_manager.get_completed_downloads()
            self.filter_downloads()
            self.update_stats()
            
            # تحديث وقت آخر تحديث
            from datetime import datetime
            self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")
            
    def filter_downloads(self):
        """تصفية التحميلات"""
        search_text = self.search_edit.text().lower()
        selected_type = self.file_type_combo.currentData()
        
        self.filtered_downloads = []
        
        for download in self.completed_downloads:
            # تصفية النص
            if search_text:
                filename = download['filename'].lower()
                url = download['url'].lower()
                if search_text not in filename and search_text not in url:
                    continue
                    
            # تصفية النوع
            if selected_type != "all":
                if download.get('file_type') != selected_type:
                    continue
                    
            self.filtered_downloads.append(download)
            
        self.populate_table()
        
    def populate_table(self):
        """ملء الجدول"""
        self.table_widget.setRowCount(len(self.filtered_downloads))
        
        for row, download in enumerate(self.filtered_downloads):
            # اسم الملف
            filename_item = QTableWidgetItem(download['filename'])
            filename_item.setIcon(QIcon(FileUtils.get_file_icon_path(download['filename'])))
            self.table_widget.setItem(row, 0, filename_item)
            
            # الحجم
            size_text = FileUtils.format_file_size(download.get('total_size', 0))
            self.table_widget.setItem(row, 1, QTableWidgetItem(size_text))
            
            # النوع
            file_info = FileUtils.get_file_info(download['filename'])
            self.table_widget.setItem(row, 2, QTableWidgetItem(file_info['description']))
            
            # تاريخ الإكمال
            if download.get('end_time'):
                from datetime import datetime
                end_time = datetime.fromtimestamp(download['end_time'])
                date_text = end_time.strftime('%Y-%m-%d %H:%M')
            else:
                date_text = "غير معروف"
            self.table_widget.setItem(row, 3, QTableWidgetItem(date_text))
            
            # السرعة المتوسطة
            speed_text = FileUtils.format_speed(download.get('speed', 0))
            self.table_widget.setItem(row, 4, QTableWidgetItem(speed_text))
            
            # المسار
            self.table_widget.setItem(row, 5, QTableWidgetItem(download['save_path']))
            
            # الحالة (التحقق من وجود الملف)
            if os.path.exists(download['save_path']):
                status_item = QTableWidgetItem("موجود")
                status_item.setForeground(Qt.darkGreen)
            else:
                status_item = QTableWidgetItem("مفقود")
                status_item.setForeground(Qt.red)
            self.table_widget.setItem(row, 6, status_item)
            
    def update_stats(self):
        """تحديث الإحصائيات"""
        total_count = len(self.filtered_downloads)
        total_size = sum(download.get('total_size', 0) for download in self.filtered_downloads)
        
        # حساب متوسط السرعة
        speeds = [download.get('speed', 0) for download in self.filtered_downloads if download.get('speed', 0) > 0]
        avg_speed = sum(speeds) / len(speeds) if speeds else 0
        
        # تحديث التسميات
        self.total_downloads_label.setText(f"إجمالي التحميلات: {total_count}")
        self.total_size_label.setText(f"إجمالي الحجم: {FileUtils.format_file_size(total_size)}")
        self.avg_speed_label.setText(f"متوسط السرعة: {FileUtils.format_speed(avg_speed)}")
        
    def show_context_menu(self, position):
        """إظهار القائمة المنبثقة"""
        if not self.table_widget.itemAt(position):
            return
            
        menu = QMenu(self)
        
        # فتح الملف
        open_action = QAction("فتح الملف", self)
        open_action.setIcon(QIcon("assets/icons/open.png"))
        open_action.triggered.connect(self.open_selected_file)
        menu.addAction(open_action)
        
        # فتح مجلد الملف
        open_folder_action = QAction("فتح المجلد", self)
        open_folder_action.setIcon(QIcon("assets/icons/folder.png"))
        open_folder_action.triggered.connect(self.open_selected_folder)
        menu.addAction(open_folder_action)
        
        menu.addSeparator()
        
        # نسخ المسار
        copy_path_action = QAction("نسخ المسار", self)
        copy_path_action.triggered.connect(self.copy_selected_path)
        menu.addAction(copy_path_action)
        
        # نسخ الرابط
        copy_url_action = QAction("نسخ الرابط", self)
        copy_url_action.triggered.connect(self.copy_selected_url)
        menu.addAction(copy_url_action)
        
        menu.addSeparator()
        
        # حذف من السجل
        delete_action = QAction("حذف من السجل", self)
        delete_action.setIcon(QIcon("assets/icons/delete.png"))
        delete_action.triggered.connect(self.delete_selected_records)
        menu.addAction(delete_action)
        
        # إعادة تحميل
        redownload_action = QAction("إعادة تحميل", self)
        redownload_action.setIcon(QIcon("assets/icons/download.png"))
        redownload_action.triggered.connect(self.redownload_selected)
        menu.addAction(redownload_action)
        
        menu.exec(self.table_widget.mapToGlobal(position))
        
    def get_selected_downloads(self) -> List[Dict]:
        """الحصول على التحميلات المحددة"""
        selected_rows = set()
        for item in self.table_widget.selectedItems():
            selected_rows.add(item.row())
            
        return [self.filtered_downloads[row] for row in selected_rows]
        
    def open_selected_file(self):
        """فتح الملف المحدد"""
        selected = self.get_selected_downloads()
        if not selected:
            return
            
        for download in selected:
            file_path = download['save_path']
            if os.path.exists(file_path):
                try:
                    if os.name == 'nt':  # Windows
                        os.startfile(file_path)
                    else:  # Linux/Mac
                        subprocess.run(['xdg-open', file_path])
                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في فتح الملف: {str(e)}")
            else:
                QMessageBox.warning(self, "خطأ", f"الملف غير موجود: {file_path}")
                
    def open_selected_folder(self):
        """فتح مجلد الملف المحدد"""
        selected = self.get_selected_downloads()
        if not selected:
            return
            
        for download in selected:
            folder_path = Path(download['save_path']).parent
            if folder_path.exists():
                try:
                    if os.name == 'nt':  # Windows
                        subprocess.run(['explorer', str(folder_path)])
                    else:  # Linux/Mac
                        subprocess.run(['xdg-open', str(folder_path)])
                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في فتح المجلد: {str(e)}")
            else:
                QMessageBox.warning(self, "خطأ", f"المجلد غير موجود: {folder_path}")
                
    def copy_selected_path(self):
        """نسخ مسار الملف المحدد"""
        selected = self.get_selected_downloads()
        if not selected:
            return
            
        from PySide6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        
        paths = [download['save_path'] for download in selected]
        clipboard.setText('\n'.join(paths))
        
    def copy_selected_url(self):
        """نسخ رابط الملف المحدد"""
        selected = self.get_selected_downloads()
        if not selected:
            return
            
        from PySide6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        
        urls = [download['url'] for download in selected]
        clipboard.setText('\n'.join(urls))
        
    def delete_selected_records(self):
        """حذف السجلات المحددة"""
        selected = self.get_selected_downloads()
        if not selected:
            return
            
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف {len(selected)} سجل من قاعدة البيانات؟\n"
            "لن يتم حذف الملفات الفعلية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for download in selected:
                self.db_manager.remove_download(download['id'])
                
            self.load_completed_downloads()
            
    def redownload_selected(self):
        """إعادة تحميل الملفات المحددة"""
        selected = self.get_selected_downloads()
        if not selected:
            return
            
        # إشارة لإعادة التحميل (يجب ربطها بالنافذة الرئيسية)
        for download in selected:
            # يمكن إضافة منطق إعادة التحميل هنا
            pass
            
    def open_downloads_folder(self):
        """فتح مجلد التحميلات الافتراضي"""
        downloads_folder = Path.home() / "Downloads"
        
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['explorer', str(downloads_folder)])
            else:  # Linux/Mac
                subprocess.run(['xdg-open', str(downloads_folder)])
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح المجلد: {str(e)}")
            
    def clear_completed_downloads(self):
        """مسح جميع التحميلات المكتملة"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع سجلات التحميلات المكتملة؟\n"
            "لن يتم حذف الملفات الفعلية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # حذف جميع التحميلات المكتملة من قاعدة البيانات
                for download in self.completed_downloads:
                    self.db_manager.remove_download(download['id'])
                    
                self.load_completed_downloads()
                QMessageBox.information(self, "تم", "تم مسح جميع السجلات بنجاح")
                
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في مسح السجلات: {str(e)}")
                
    def export_completed_list(self):
        """تصدير قائمة التحميلات المكتملة"""
        if not self.completed_downloads:
            QMessageBox.information(self, "تنبيه", "لا توجد تحميلات مكتملة للتصدير")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير قائمة التحميلات",
            "completed_downloads.json",
            "JSON Files (*.json);;CSV Files (*.csv)"
        )
        
        if file_path:
            try:
                if file_path.endswith('.json'):
                    self.export_to_json(file_path)
                elif file_path.endswith('.csv'):
                    self.export_to_csv(file_path)
                    
                QMessageBox.information(self, "تم", f"تم تصدير القائمة إلى: {file_path}")
                
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في التصدير: {str(e)}")
                
    def export_to_json(self, file_path: str):
        """تصدير إلى JSON"""
        import json
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.completed_downloads, f, ensure_ascii=False, indent=2)
            
    def export_to_csv(self, file_path: str):
        """تصدير إلى CSV"""
        import csv
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # كتابة العناوين
            writer.writerow([
                'اسم الملف', 'الرابط', 'الحجم', 'النوع', 
                'تاريخ الإكمال', 'السرعة', 'المسار'
            ])
            
            # كتابة البيانات
            for download in self.completed_downloads:
                writer.writerow([
                    download['filename'],
                    download['url'],
                    download.get('total_size', 0),
                    download.get('file_type', ''),
                    download.get('end_time', ''),
                    download.get('speed', 0),
                    download['save_path']
                ])