#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عنصر التحميل الفردي
Individual Download Item Widget
"""

import time
from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QProgressBar,
    QPushButton, QFrame, QMenu, QSpinBox, QToolTip, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QIcon, QPixmap, QFont, QColor, QPainter, QPen

from core.download_manager import DownloadInfo, DownloadStatus
from core.file_utils import FileUtils

class DownloadItemWidget(QWidget):
    """عنصر التحميل الفردي"""
    
    # الإشارات
    pause_requested = Signal(str)  # download_id
    resume_requested = Signal(str)  # download_id
    cancel_requested = Signal(str)  # download_id
    remove_requested = Signal(str)  # download_id
    priority_changed = Signal(str, int)  # download_id, priority
    item_selected = Signal(str)  # download_id
    
    def __init__(self, download_info: DownloadInfo, download_manager):
        super().__init__()
        self.download_info = download_info
        self.download_manager = download_manager
        self.is_selected = False
        self.speed_history = []
        self.last_update_time = time.time()
        
        self.setup_ui()
        self.setup_animations()
        self.update_display()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFixedHeight(120)
        self.setStyleSheet("""
            QWidget {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 2px;
            }
            QWidget:hover {
                border-color: #2196F3;
                background-color: #F8F9FA;
            }
        """)
        
        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(15, 10, 15, 10)
        main_layout.setSpacing(15)
        
        # أيقونة نوع الملف
        self.create_file_icon()
        main_layout.addWidget(self.file_icon_label)
        
        # معلومات الملف
        self.create_file_info()
        main_layout.addLayout(self.info_layout)
        
        # شريط التقدم والإحصائيات
        self.create_progress_section()
        main_layout.addLayout(self.progress_layout)
        
        # أزرار التحكم
        self.create_control_buttons()
        main_layout.addLayout(self.buttons_layout)
        
        # إعداد القائمة المنبثقة
        self.setup_context_menu()
        
    def create_file_icon(self):
        """إنشاء أيقونة نوع الملف"""
        self.file_icon_label = QLabel()
        self.file_icon_label.setFixedSize(48, 48)
        self.file_icon_label.setAlignment(Qt.AlignCenter)
        
        # تحديد الأيقونة حسب نوع الملف
        icon_path = FileUtils.get_file_icon_path(self.download_info.filename)
        pixmap = QPixmap(icon_path)
        if pixmap.isNull():
            # أيقونة افتراضية
            pixmap = QPixmap(48, 48)
            pixmap.fill(QColor("#2196F3"))
            
        self.file_icon_label.setPixmap(pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
    def create_file_info(self):
        """إنشاء معلومات الملف"""
        self.info_layout = QVBoxLayout()
        self.info_layout.setSpacing(4)
        
        # اسم الملف
        self.filename_label = QLabel(self.download_info.filename)
        self.filename_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.filename_label.setWordWrap(True)
        self.filename_label.setMaximumWidth(300)
        
        # الرابط
        self.url_label = QLabel(self.truncate_url(self.download_info.url))
        self.url_label.setFont(QFont("Arial", 8))
        self.url_label.setStyleSheet("color: #666666;")
        self.url_label.setMaximumWidth(300)
        
        # معلومات إضافية
        self.info_label = QLabel()
        self.info_label.setFont(QFont("Arial", 8))
        self.info_label.setStyleSheet("color: #888888;")
        
        self.info_layout.addWidget(self.filename_label)
        self.info_layout.addWidget(self.url_label)
        self.info_layout.addWidget(self.info_label)
        self.info_layout.addStretch()
        
    def create_progress_section(self):
        """إنشاء قسم التقدم"""
        self.progress_layout = QVBoxLayout()
        self.progress_layout.setSpacing(5)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                background-color: #F5F5F5;
            }
            QProgressBar::chunk {
                background-color: #2196F3;
                border-radius: 3px;
            }
        """)
        
        # معلومات التقدم
        self.progress_info_layout = QHBoxLayout()
        
        self.progress_label = QLabel("0%")
        self.progress_label.setFont(QFont("Arial", 9, QFont.Bold))
        
        self.size_label = QLabel("0 B / 0 B")
        self.size_label.setFont(QFont("Arial", 8))
        self.size_label.setStyleSheet("color: #666666;")
        
        self.speed_label = QLabel("0 KB/s")
        self.speed_label.setFont(QFont("Arial", 8))
        self.speed_label.setStyleSheet("color: #2196F3;")
        
        self.eta_label = QLabel("--:--")
        self.eta_label.setFont(QFont("Arial", 8))
        self.eta_label.setStyleSheet("color: #666666;")
        
        self.progress_info_layout.addWidget(self.progress_label)
        self.progress_info_layout.addStretch()
        self.progress_info_layout.addWidget(self.size_label)
        self.progress_info_layout.addStretch()
        self.progress_info_layout.addWidget(self.speed_label)
        self.progress_info_layout.addStretch()
        self.progress_info_layout.addWidget(self.eta_label)
        
        # حالة التحميل
        self.status_label = QLabel()
        self.status_label.setFont(QFont("Arial", 8, QFont.Bold))
        
        self.progress_layout.addWidget(self.progress_bar)
        self.progress_layout.addLayout(self.progress_info_layout)
        self.progress_layout.addWidget(self.status_label)
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        self.buttons_layout = QVBoxLayout()
        self.buttons_layout.setSpacing(5)
        
        # زر الإيقاف/الاستئناف
        self.pause_resume_btn = QPushButton()
        self.pause_resume_btn.setFixedSize(32, 32)
        self.pause_resume_btn.clicked.connect(self.toggle_pause_resume)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton()
        self.cancel_btn.setFixedSize(32, 32)
        self.cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        self.cancel_btn.setToolTip("إلغاء التحميل")
        self.cancel_btn.clicked.connect(lambda: self.cancel_requested.emit(self.download_info.id))
        
        # زر الأولوية
        self.priority_btn = QPushButton("أولوية")
        self.priority_btn.setFixedSize(60, 24)
        self.priority_btn.clicked.connect(self.show_priority_menu)
        
        self.buttons_layout.addWidget(self.pause_resume_btn)
        self.buttons_layout.addWidget(self.cancel_btn)
        self.buttons_layout.addWidget(self.priority_btn)
        self.buttons_layout.addStretch()
        
    def setup_context_menu(self):
        """إعداد القائمة المنبثقة"""
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        self.progress_animation = QPropertyAnimation(self.progress_bar, b"value")
        self.progress_animation.setDuration(300)
        self.progress_animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def truncate_url(self, url: str, max_length: int = 50) -> str:
        """اختصار الرابط"""
        if len(url) <= max_length:
            return url
        return url[:max_length-3] + "..."
        
    def update_display(self):
        """تحديث العرض"""
        # تحديث معلومات الملف
        file_info = FileUtils.get_file_info(self.download_info.filename)
        self.info_label.setText(f"النوع: {file_info['description']} | الأولوية: {self.download_info.priority}")
        
        # تحديث الحالة
        self.update_status_display()
        
        # تحديث أزرار التحكم
        self.update_control_buttons()
        
    def update_status_display(self):
        """تحديث عرض الحالة"""
        status = self.download_info.status
        
        status_colors = {
            DownloadStatus.DOWNLOADING: "#2196F3",
            DownloadStatus.PAUSED: "#FF9800",
            DownloadStatus.COMPLETED: "#4CAF50",
            DownloadStatus.FAILED: "#F44336",
            DownloadStatus.WAITING: "#9E9E9E",
            DownloadStatus.CANCELLED: "#757575"
        }
        
        status_texts = {
            DownloadStatus.DOWNLOADING: "جاري التحميل",
            DownloadStatus.PAUSED: "متوقف",
            DownloadStatus.COMPLETED: "مكتمل",
            DownloadStatus.FAILED: "فشل",
            DownloadStatus.WAITING: "في الانتظار",
            DownloadStatus.CANCELLED: "ملغي"
        }
        
        color = status_colors.get(status, "#000000")
        text = status_texts.get(status, "غير معروف")
        
        self.status_label.setText(text)
        self.status_label.setStyleSheet(f"color: {color};")
        
        # تحديث لون شريط التقدم
        if status == DownloadStatus.DOWNLOADING:
            chunk_color = "#2196F3"
        elif status == DownloadStatus.PAUSED:
            chunk_color = "#FF9800"
        elif status == DownloadStatus.COMPLETED:
            chunk_color = "#4CAF50"
        elif status == DownloadStatus.FAILED:
            chunk_color = "#F44336"
        else:
            chunk_color = "#9E9E9E"
            
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                background-color: #F5F5F5;
            }}
            QProgressBar::chunk {{
                background-color: {chunk_color};
                border-radius: 3px;
            }}
        """)
        
    def update_control_buttons(self):
        """تحديث أزرار التحكم"""
        status = self.download_info.status
        
        if status == DownloadStatus.DOWNLOADING:
            self.pause_resume_btn.setIcon(QIcon("assets/icons/pause.png"))
            self.pause_resume_btn.setToolTip("إيقاف التحميل")
        elif status in [DownloadStatus.PAUSED, DownloadStatus.WAITING]:
            self.pause_resume_btn.setIcon(QIcon("assets/icons/resume.png"))
            self.pause_resume_btn.setToolTip("استئناف التحميل")
        else:
            self.pause_resume_btn.setEnabled(False)
            
        # إخفاء زر الإلغاء للتحميلات المكتملة
        if status in [DownloadStatus.COMPLETED, DownloadStatus.CANCELLED]:
            self.cancel_btn.setVisible(False)
        else:
            self.cancel_btn.setVisible(True)
            
    def update_progress(self, progress: float, speed: float, downloaded: int, total: int):
        """تحديث التقدم"""
        # تحديث شريط التقدم مع رسوم متحركة
        current_value = self.progress_bar.value()
        target_value = int(progress)
        
        if abs(target_value - current_value) > 1:
            self.progress_animation.setStartValue(current_value)
            self.progress_animation.setEndValue(target_value)
            self.progress_animation.start()
        else:
            self.progress_bar.setValue(target_value)
            
        # تحديث النصوص
        self.progress_label.setText(f"{progress:.1f}%")
        self.size_label.setText(f"{FileUtils.format_file_size(downloaded)} / {FileUtils.format_file_size(total)}")
        self.speed_label.setText(FileUtils.format_speed(speed))
        
        # حساب الوقت المتبقي
        if speed > 0 and total > downloaded:
            eta = FileUtils.calculate_eta(downloaded, total, speed)
            self.eta_label.setText(eta)
        else:
            self.eta_label.setText("--:--")
            
        # حفظ السرعة في التاريخ
        current_time = time.time()
        self.speed_history.append((current_time, speed))
        
        # الاحتفاظ بآخر 60 نقطة فقط (دقيقة واحدة)
        if len(self.speed_history) > 60:
            self.speed_history.pop(0)
            
    def update_status(self, status: str):
        """تحديث الحالة"""
        self.download_info.status = DownloadStatus(status)
        self.update_status_display()
        self.update_control_buttons()
        
    def set_error_message(self, error: str):
        """تعيين رسالة الخطأ"""
        self.download_info.error_message = error
        self.status_label.setToolTip(f"خطأ: {error}")
        
    def toggle_pause_resume(self):
        """تبديل الإيقاف/الاستئناف"""
        if self.download_info.status == DownloadStatus.DOWNLOADING:
            self.pause_requested.emit(self.download_info.id)
        elif self.download_info.status in [DownloadStatus.PAUSED, DownloadStatus.WAITING]:
            self.resume_requested.emit(self.download_info.id)
            
    def show_priority_menu(self):
        """إظهار قائمة الأولوية"""
        menu = QMenu(self)
        
        priorities = [
            (5, "عالية جداً"),
            (4, "عالية"),
            (3, "متوسطة"),
            (2, "منخفضة"),
            (1, "منخفضة جداً")
        ]
        
        for priority, name in priorities:
            action = menu.addAction(name)
            action.setCheckable(True)
            action.setChecked(self.download_info.priority == priority)
            action.triggered.connect(lambda checked, p=priority: self.set_priority(p))
            
        menu.exec(self.priority_btn.mapToGlobal(self.priority_btn.rect().bottomLeft()))
        
    def set_priority(self, priority: int):
        """تعيين الأولوية"""
        self.download_info.priority = priority
        self.priority_changed.emit(self.download_info.id, priority)
        self.update_display()
        
    def show_context_menu(self, position):
        """إظهار القائمة المنبثقة"""
        menu = QMenu(self)
        
        # إضافة الإجراءات
        if self.download_info.status == DownloadStatus.DOWNLOADING:
            pause_action = menu.addAction("إيقاف")
            pause_action.triggered.connect(lambda: self.pause_requested.emit(self.download_info.id))
        elif self.download_info.status in [DownloadStatus.PAUSED, DownloadStatus.WAITING]:
            resume_action = menu.addAction("استئناف")
            resume_action.triggered.connect(lambda: self.resume_requested.emit(self.download_info.id))
            
        if self.download_info.status not in [DownloadStatus.COMPLETED, DownloadStatus.CANCELLED]:
            cancel_action = menu.addAction("إلغاء")
            cancel_action.triggered.connect(lambda: self.cancel_requested.emit(self.download_info.id))
            
        menu.addSeparator()
        
        remove_action = menu.addAction("حذف")
        remove_action.triggered.connect(lambda: self.remove_requested.emit(self.download_info.id))
        
        copy_url_action = menu.addAction("نسخ الرابط")
        copy_url_action.triggered.connect(self.copy_url)
        
        if self.download_info.status == DownloadStatus.COMPLETED:
            open_file_action = menu.addAction("فتح الملف")
            open_file_action.triggered.connect(self.open_file)
            
            open_folder_action = menu.addAction("فتح المجلد")
            open_folder_action.triggered.connect(self.open_folder)
            
        menu.exec(self.mapToGlobal(position))
        
    def copy_url(self):
        """نسخ الرابط"""
        from PySide6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(self.download_info.url)
        
    def open_file(self):
        """فتح الملف"""
        import os
        import subprocess
        
        if os.path.exists(self.download_info.save_path):
            if os.name == 'nt':  # Windows
                os.startfile(self.download_info.save_path)
            else:  # Linux/Mac
                subprocess.run(['xdg-open', self.download_info.save_path])
                
    def open_folder(self):
        """فتح مجلد الملف"""
        import os
        import subprocess
        from pathlib import Path
        
        folder_path = Path(self.download_info.save_path).parent
        if folder_path.exists():
            if os.name == 'nt':  # Windows
                subprocess.run(['explorer', str(folder_path)])
            else:  # Linux/Mac
                subprocess.run(['xdg-open', str(folder_path)])
                
    def mousePressEvent(self, event):
        """عند النقر على العنصر"""
        if event.button() == Qt.LeftButton:
            self.item_selected.emit(self.download_info.id)
            self.set_selected(True)
        super().mousePressEvent(event)
        
    def set_selected(self, selected: bool):
        """تعيين حالة التحديد"""
        self.is_selected = selected
        if selected:
            self.setStyleSheet("""
                QWidget {
                    background-color: #E3F2FD;
                    border: 2px solid #2196F3;
                    border-radius: 8px;
                    margin: 2px;
                }
            """)
        else:
            self.setStyleSheet("""
                QWidget {
                    background-color: #FFFFFF;
                    border: 1px solid #E0E0E0;
                    border-radius: 8px;
                    margin: 2px;
                }
                QWidget:hover {
                    border-color: #2196F3;
                    background-color: #F8F9FA;
                }
            """)
            
    def get_speed_history(self) -> list:
        """الحصول على تاريخ السرعة"""
        return self.speed_history.copy()