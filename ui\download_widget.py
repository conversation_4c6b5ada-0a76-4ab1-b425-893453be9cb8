#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة عرض التحميلات الحالية
Current Downloads Widget
"""

import time
from typing import Dict, List
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QProgressBar, QFrame, QMenu, QMessageBox,
    QSplitter, QGroupBox, QGridLayout, QScrollArea
)
from PySide6.QtCore import Qt, QTimer, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap, QAction, QFont

from core.download_manager import DownloadManager, DownloadInfo, DownloadStatus
from core.file_utils import FileUtils
from ui.download_item_widget import DownloadItemWidget

class DownloadWidget(QWidget):
    """واجهة التحميلات الحالية"""
    
    # الإشارات
    download_selected = Signal(str)  # download_id
    
    def __init__(self, download_manager: DownloadManager):
        super().__init__()
        self.download_manager = download_manager
        self.download_items: Dict[str, DownloadItemWidget] = {}
        self.filtered_items: List[str] = []
        self.current_filter = ""
        
        self.setup_ui()
        self.connect_signals()
        self.setup_update_timer()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # إنشاء منطقة التحميلات
        self.create_downloads_area()
        layout.addWidget(self.downloads_area)
        
        # إنشاء شريط الإحصائيات
        self.create_stats_bar()
        layout.addWidget(self.stats_frame)
        
    def create_downloads_area(self):
        """إنشاء منطقة عرض التحميلات"""
        # إنشاء مقسم للتخطيط
        self.downloads_area = QSplitter(Qt.Vertical)
        
        # منطقة التحميلات النشطة
        active_group = QGroupBox("التحميلات النشطة")
        active_layout = QVBoxLayout(active_group)
        
        # منطقة التمرير للتحميلات النشطة
        self.active_scroll = QScrollArea()
        self.active_scroll.setWidgetResizable(True)
        self.active_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.active_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.active_container = QWidget()
        self.active_layout = QVBoxLayout(self.active_container)
        self.active_layout.setAlignment(Qt.AlignTop)
        self.active_layout.setSpacing(5)
        
        self.active_scroll.setWidget(self.active_container)
        active_layout.addWidget(self.active_scroll)
        
        # منطقة قائمة الانتظار
        queue_group = QGroupBox("قائمة الانتظار")
        queue_layout = QVBoxLayout(queue_group)
        
        self.queue_scroll = QScrollArea()
        self.queue_scroll.setWidgetResizable(True)
        self.queue_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.queue_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.queue_container = QWidget()
        self.queue_layout = QVBoxLayout(self.queue_container)
        self.queue_layout.setAlignment(Qt.AlignTop)
        self.queue_layout.setSpacing(5)
        
        self.queue_scroll.setWidget(self.queue_container)
        queue_layout.addWidget(self.queue_scroll)
        
        # إضافة المجموعات إلى المقسم
        self.downloads_area.addWidget(active_group)
        self.downloads_area.addWidget(queue_group)
        self.downloads_area.setSizes([400, 200])
        
    def create_stats_bar(self):
        """إنشاء شريط الإحصائيات"""
        self.stats_frame = QFrame()
        self.stats_frame.setFrameStyle(QFrame.StyledPanel)
        self.stats_frame.setFixedHeight(60)
        
        stats_layout = QHBoxLayout(self.stats_frame)
        stats_layout.setContentsMargins(15, 10, 15, 10)
        
        # إجمالي السرعة
        self.total_speed_label = QLabel("السرعة الإجمالية: 0 KB/s")
        self.total_speed_label.setFont(QFont("Arial", 10, QFont.Bold))
        
        # عدد التحميلات النشطة
        self.active_count_label = QLabel("التحميلات النشطة: 0")
        
        # عدد قائمة الانتظار
        self.queue_count_label = QLabel("قائمة الانتظار: 0")
        
        # إجمالي البيانات المحملة اليوم
        self.daily_data_label = QLabel("البيانات اليوم: 0 MB")
        
        # إضافة التسميات
        stats_layout.addWidget(self.total_speed_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.active_count_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.queue_count_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.daily_data_label)
        
    def connect_signals(self):
        """ربط الإشارات"""
        # ربط إشارات مدير التحميلات
        self.download_manager.download_added.connect(self.on_download_added)
        self.download_manager.download_progress.connect(self.on_download_progress)
        self.download_manager.download_status_changed.connect(self.on_download_status_changed)
        self.download_manager.download_completed.connect(self.on_download_completed)
        self.download_manager.download_failed.connect(self.on_download_failed)
        
    def setup_update_timer(self):
        """إعداد تايمر التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_stats)
        self.update_timer.start(1000)  # تحديث كل ثانية
        
    def on_download_added(self, download_id: str):
        """عند إضافة تحميل جديد"""
        download_info = self.download_manager.get_download_info(download_id)
        if download_info:
            self.add_download_item(download_info)
            
    def add_download_item(self, download_info: DownloadInfo):
        """إضافة عنصر تحميل جديد"""
        if download_info.id in self.download_items:
            return
            
        # إنشاء عنصر التحميل
        item_widget = DownloadItemWidget(download_info, self.download_manager)
        
        # ربط الإشارات
        item_widget.pause_requested.connect(self.pause_download)
        item_widget.resume_requested.connect(self.resume_download)
        item_widget.cancel_requested.connect(self.cancel_download)
        item_widget.remove_requested.connect(self.remove_download)
        item_widget.priority_changed.connect(self.change_priority)
        item_widget.item_selected.connect(self.download_selected.emit)
        
        # إضافة إلى القائمة المناسبة
        if download_info.status == DownloadStatus.DOWNLOADING:
            self.active_layout.addWidget(item_widget)
        else:
            self.queue_layout.addWidget(item_widget)
            
        self.download_items[download_info.id] = item_widget
        self.update_visibility()
        
    def on_download_progress(self, download_id: str, progress: float, speed: float, downloaded: int, total: int):
        """عند تحديث تقدم التحميل"""
        if download_id in self.download_items:
            self.download_items[download_id].update_progress(progress, speed, downloaded, total)
            
    def on_download_status_changed(self, download_id: str, status: str):
        """عند تغيير حالة التحميل"""
        if download_id in self.download_items:
            item_widget = self.download_items[download_id]
            item_widget.update_status(status)
            
            # نقل العنصر إلى القائمة المناسبة
            self.move_item_to_appropriate_container(download_id, status)
            
    def move_item_to_appropriate_container(self, download_id: str, status: str):
        """نقل العنصر إلى الحاوية المناسبة"""
        if download_id not in self.download_items:
            return
            
        item_widget = self.download_items[download_id]
        
        # إزالة من الحاوية الحالية
        if item_widget.parent():
            item_widget.parent().layout().removeWidget(item_widget)
            
        # إضافة إلى الحاوية المناسبة
        if status == "downloading":
            self.active_layout.addWidget(item_widget)
        else:
            self.queue_layout.addWidget(item_widget)
            
    def on_download_completed(self, download_id: str):
        """عند اكتمال التحميل"""
        if download_id in self.download_items:
            item_widget = self.download_items[download_id]
            item_widget.update_status("completed")
            
            # إزالة من القائمة بعد فترة
            QTimer.singleShot(5000, lambda: self.remove_completed_item(download_id))
            
    def on_download_failed(self, download_id: str, error: str):
        """عند فشل التحميل"""
        if download_id in self.download_items:
            item_widget = self.download_items[download_id]
            item_widget.update_status("failed")
            item_widget.set_error_message(error)
            
    def remove_completed_item(self, download_id: str):
        """إزالة العنصر المكتمل"""
        if download_id in self.download_items:
            item_widget = self.download_items[download_id]
            if item_widget.download_info.status == DownloadStatus.COMPLETED:
                self.remove_download_item(download_id)
                
    def pause_download(self, download_id: str):
        """إيقاف التحميل"""
        self.download_manager.pause_download(download_id)
        
    def resume_download(self, download_id: str):
        """استئناف التحميل"""
        self.download_manager.resume_download(download_id)
        
    def cancel_download(self, download_id: str):
        """إلغاء التحميل"""
        reply = QMessageBox.question(
            self, "تأكيد الإلغاء",
            "هل أنت متأكد من إلغاء هذا التحميل؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.download_manager.cancel_download(download_id)
            
    def remove_download(self, download_id: str):
        """حذف التحميل"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا التحميل؟\nسيتم حذف الملف المحمل أيضاً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.download_manager.remove_download(download_id)
            self.remove_download_item(download_id)
            
    def remove_download_item(self, download_id: str):
        """إزالة عنصر التحميل من الواجهة"""
        if download_id in self.download_items:
            item_widget = self.download_items[download_id]
            if item_widget.parent():
                item_widget.parent().layout().removeWidget(item_widget)
            item_widget.deleteLater()
            del self.download_items[download_id]
            self.update_visibility()
            
    def change_priority(self, download_id: str, priority: int):
        """تغيير أولوية التحميل"""
        download_info = self.download_manager.get_download_info(download_id)
        if download_info:
            download_info.priority = priority
            # إعادة ترتيب قائمة الانتظار
            self.reorder_queue()
            
    def reorder_queue(self):
        """إعادة ترتيب قائمة الانتظار حسب الأولوية"""
        # الحصول على جميع عناصر قائمة الانتظار
        queue_items = []
        for i in range(self.queue_layout.count()):
            item = self.queue_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, DownloadItemWidget):
                    queue_items.append(widget)
                    
        # ترتيب حسب الأولوية
        queue_items.sort(key=lambda x: x.download_info.priority, reverse=True)
        
        # إعادة إضافة العناصر بالترتيب الجديد
        for widget in queue_items:
            self.queue_layout.removeWidget(widget)
            
        for widget in queue_items:
            self.queue_layout.addWidget(widget)
            
    def filter_downloads(self, filter_text: str):
        """تصفية التحميلات"""
        self.current_filter = filter_text.lower()
        self.update_visibility()
        
    def update_visibility(self):
        """تحديث رؤية العناصر حسب التصفية"""
        for download_id, item_widget in self.download_items.items():
            if self.current_filter:
                # البحث في اسم الملف والرابط
                filename = item_widget.download_info.filename.lower()
                url = item_widget.download_info.url.lower()
                
                visible = (self.current_filter in filename or 
                          self.current_filter in url)
            else:
                visible = True
                
            item_widget.setVisible(visible)
            
    def update_stats(self):
        """تحديث الإحصائيات"""
        # حساب السرعة الإجمالية
        total_speed = self.download_manager.get_total_speed()
        self.total_speed_label.setText(f"السرعة الإجمالية: {FileUtils.format_speed(total_speed)}")
        
        # عدد التحميلات النشطة
        active_count = sum(1 for item in self.download_items.values() 
                          if item.download_info.status == DownloadStatus.DOWNLOADING)
        self.active_count_label.setText(f"التحميلات النشطة: {active_count}")
        
        # عدد قائمة الانتظار
        queue_count = sum(1 for item in self.download_items.values() 
                         if item.download_info.status == DownloadStatus.WAITING)
        self.queue_count_label.setText(f"قائمة الانتظار: {queue_count}")
        
        # البيانات المحملة اليوم (تحتاج إلى تنفيذ في قاعدة البيانات)
        # self.daily_data_label.setText(f"البيانات اليوم: {daily_data}")
        
    def clear_completed_downloads(self):
        """مسح التحميلات المكتملة"""
        completed_items = [
            download_id for download_id, item in self.download_items.items()
            if item.download_info.status == DownloadStatus.COMPLETED
        ]
        
        for download_id in completed_items:
            self.remove_download_item(download_id)
            
    def pause_all_downloads(self):
        """إيقاف جميع التحميلات"""
        for item in self.download_items.values():
            if item.download_info.status == DownloadStatus.DOWNLOADING:
                self.pause_download(item.download_info.id)
                
    def resume_all_downloads(self):
        """استئناف جميع التحميلات"""
        for item in self.download_items.values():
            if item.download_info.status == DownloadStatus.PAUSED:
                self.resume_download(item.download_info.id)
                
    def get_download_count(self) -> Dict[str, int]:
        """الحصول على عدد التحميلات حسب الحالة"""
        counts = {
            "total": len(self.download_items),
            "downloading": 0,
            "paused": 0,
            "waiting": 0,
            "completed": 0,
            "failed": 0
        }
        
        for item in self.download_items.values():
            status = item.download_info.status.value
            if status in counts:
                counts[status] += 1
                
        return counts