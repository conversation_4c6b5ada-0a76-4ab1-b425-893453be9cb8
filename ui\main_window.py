#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Professional Download Manager Main Window
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QStackedWidget, QTabWidget, QPushButton, QLabel,
    QLineEdit, QProgressBar, QListWidget, QListWidgetItem,
    QMenuBar, QMenu, QStatusBar, QSystemTrayIcon, QApplication,
    QMessageBox, QFileDialog, QComboBox, QCheckBox,
    QGroupBox, QGridLayout, QSplitter, QFrame
)
from PySide6.QtCore import (
    Qt, QTimer, QThread, Signal, QSize, QSettings,
    QUrl, QMimeData
)
from PySide6.QtGui import (
    QIcon, QPixmap, QAction, <PERSON><PERSON><PERSON>, QPalette, QColor,
    QDragEnterEvent, QDropEvent
)

# استيراد الوحدات المخصصة
from ui.download_widget import DownloadWidget
from ui.completed_widget import CompletedWidget  
from ui.analytics_widget import AnalyticsWidget
from ui.settings_widget import SettingsWidget
from ui.add_download_dialog import AddDownloadDialog
from core.download_manager import DownloadManager
from core.database import DatabaseManager
from core.theme_manager import ThemeManager

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # إعداد المتغيرات الأساسية
        self.settings = QSettings()
        self.db_manager = DatabaseManager()
        self.download_manager = DownloadManager()
        self.theme_manager = ThemeManager()
        
        # إعداد النافذة
        self.setup_ui()
        self.setup_system_tray()
        self.setup_clipboard_monitor()
        self.load_settings()
        
        # تطبيق الثيم
        self.apply_theme()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("Professional Download Manager")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # تمكين السحب والإفلات
        self.setAcceptDrops(True)
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        main_layout.addWidget(self.toolbar)
        
        # إنشاء التبويبات الرئيسية
        self.create_main_tabs()
        main_layout.addWidget(self.main_tabs)
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إنشاء القائمة
        self.create_menu_bar()
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = QFrame()
        self.toolbar.setFixedHeight(60)
        self.toolbar.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border-bottom: 2px solid #3d3d3d;
            }
        """)
        
        toolbar_layout = QHBoxLayout(self.toolbar)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        
        # زر إضافة تحميل جديد
        self.add_download_btn = QPushButton("إضافة تحميل")
        self.add_download_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_download_btn.setFixedSize(120, 40)
        self.add_download_btn.clicked.connect(self.show_add_download_dialog)
        
        # زر إيقاف جميع التحميلات
        self.pause_all_btn = QPushButton("إيقاف الكل")
        self.pause_all_btn.setIcon(QIcon("assets/icons/pause.png"))
        self.pause_all_btn.setFixedSize(100, 40)
        self.pause_all_btn.clicked.connect(self.pause_all_downloads)
        
        # زر استئناف جميع التحميلات
        self.resume_all_btn = QPushButton("استئناف الكل")
        self.resume_all_btn.setIcon(QIcon("assets/icons/resume.png"))
        self.resume_all_btn.setFixedSize(100, 40)
        self.resume_all_btn.clicked.connect(self.resume_all_downloads)
        
        # مربع البحث
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("البحث في التحميلات...")
        self.search_box.setFixedHeight(40)
        self.search_box.textChanged.connect(self.filter_downloads)
        
        # إضافة العناصر إلى الشريط
        toolbar_layout.addWidget(self.add_download_btn)
        toolbar_layout.addWidget(self.pause_all_btn)
        toolbar_layout.addWidget(self.resume_all_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("البحث:"))
        toolbar_layout.addWidget(self.search_box)
        
    def create_main_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        self.main_tabs = QTabWidget()
        self.main_tabs.setTabPosition(QTabWidget.North)
        
        # تبويب التحميلات الحالية
        self.downloads_widget = DownloadWidget(self.download_manager)
        self.main_tabs.addTab(self.downloads_widget, "التحميلات")
        
        # تبويب التحميلات المكتملة
        self.completed_widget = CompletedWidget(self.db_manager)
        self.main_tabs.addTab(self.completed_widget, "المكتملة")
        
        # تبويب التحليل البياني
        self.analytics_widget = AnalyticsWidget(self.db_manager)
        self.main_tabs.addTab(self.analytics_widget, "الإحصائيات")
        
        # تبويب الإعدادات
        self.settings_widget = SettingsWidget(self.settings, self.theme_manager)
        self.main_tabs.addTab(self.settings_widget, "الإعدادات")
        
        # ربط إشارات التبديل بين التبويبات
        self.main_tabs.currentChanged.connect(self.on_tab_changed)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # معلومات السرعة الإجمالية
        self.speed_label = QLabel("السرعة: 0 KB/s")
        self.status_bar.addWidget(self.speed_label)
        
        # عدد التحميلات النشطة
        self.active_downloads_label = QLabel("التحميلات النشطة: 0")
        self.status_bar.addWidget(self.active_downloads_label)
        
        # مساحة القرص المتاحة
        self.disk_space_label = QLabel("المساحة المتاحة: -- GB")
        self.status_bar.addPermanentWidget(self.disk_space_label)
        
    def create_menu_bar(self):
        """إنشاء شريط القائمة"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        add_action = QAction("إضافة تحميل", self)
        add_action.setShortcut("Ctrl+N")
        add_action.triggered.connect(self.show_add_download_dialog)
        file_menu.addAction(add_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        
        settings_action = QAction("الإعدادات", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(lambda: self.main_tabs.setCurrentIndex(3))
        edit_menu.addAction(settings_action)
        
        # قائمة عرض
        view_menu = menubar.addMenu("عرض")
        
        self.dark_mode_action = QAction("الوضع الليلي", self)
        self.dark_mode_action.setCheckable(True)
        self.dark_mode_action.triggered.connect(self.toggle_dark_mode)
        view_menu.addAction(self.dark_mode_action)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_system_tray(self):
        """إعداد أيقونة النظام"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            self.tray_icon.setIcon(QIcon("assets/icons/app_icon.png"))
            
            # قائمة أيقونة النظام
            tray_menu = QMenu()
            
            show_action = QAction("إظهار", self)
            show_action.triggered.connect(self.show)
            tray_menu.addAction(show_action)
            
            hide_action = QAction("إخفاء", self)
            hide_action.triggered.connect(self.hide)
            tray_menu.addAction(hide_action)
            
            tray_menu.addSeparator()
            
            quit_action = QAction("خروج", self)
            quit_action.triggered.connect(self.close)
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.activated.connect(self.tray_icon_activated)
            self.tray_icon.show()
            
    def setup_clipboard_monitor(self):
        """إعداد مراقب الحافظة"""
        self.clipboard = QApplication.clipboard()
        self.clipboard.dataChanged.connect(self.check_clipboard_for_urls)
        
        # تايمر لفحص الحافظة بشكل دوري
        self.clipboard_timer = QTimer()
        self.clipboard_timer.timeout.connect(self.check_clipboard_for_urls)
        self.clipboard_timer.start(1000)  # فحص كل ثانية
        
    def check_clipboard_for_urls(self):
        """فحص الحافظة للبحث عن روابط"""
        try:
            clipboard_text = self.clipboard.text()
            if clipboard_text and (clipboard_text.startswith('http://') or 
                                 clipboard_text.startswith('https://')):
                # التحقق من أن الرابط جديد
                if not hasattr(self, 'last_clipboard_url') or self.last_clipboard_url != clipboard_text:
                    self.last_clipboard_url = clipboard_text
                    if self.settings.value("auto_detect_clipboard", True, type=bool):
                        self.show_add_download_dialog(clipboard_text)
        except Exception as e:
            print(f"خطأ في فحص الحافظة: {e}")
            
    def show_add_download_dialog(self, url=""):
        """إظهار حوار إضافة تحميل جديد"""
        dialog = AddDownloadDialog(self, url)
        if dialog.exec():
            download_info = dialog.get_download_info()
            self.download_manager.add_download(download_info)
            
    def pause_all_downloads(self):
        """إيقاف جميع التحميلات"""
        self.download_manager.pause_all()
        
    def resume_all_downloads(self):
        """استئناف جميع التحميلات"""
        self.download_manager.resume_all()
        
    def filter_downloads(self, text):
        """تصفية التحميلات حسب النص"""
        self.downloads_widget.filter_downloads(text)
        
    def on_tab_changed(self, index):
        """عند تغيير التبويب"""
        if index == 2:  # تبويب الإحصائيات
            self.analytics_widget.refresh_data()
            
    def toggle_dark_mode(self):
        """تبديل الوضع الليلي"""
        is_dark = self.dark_mode_action.isChecked()
        self.theme_manager.set_dark_mode(is_dark)
        self.apply_theme()
        
    def apply_theme(self):
        """تطبيق الثيم"""
        stylesheet = self.theme_manager.get_stylesheet()
        self.setStyleSheet(stylesheet)
        
    def show_about(self):
        """إظهار معلومات حول التطبيق"""
        QMessageBox.about(self, "حول التطبيق", 
                         "Professional Download Manager v1.0\n"
                         "مدير تحميلات احترافي بواجهة عربية\n"
                         "تم التطوير باستخدام Python و PySide6")
        
    def tray_icon_activated(self, reason):
        """عند النقر على أيقونة النظام"""
        if reason == QSystemTrayIcon.DoubleClick:
            if self.isVisible():
                self.hide()
            else:
                self.show()
                self.raise_()
                self.activateWindow()
                
    def load_settings(self):
        """تحميل الإعدادات"""
        # استعادة حجم وموضع النافذة
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
            
        # استعادة حالة الوضع الليلي
        is_dark = self.settings.value("dark_mode", False, type=bool)
        self.dark_mode_action.setChecked(is_dark)
        self.theme_manager.set_dark_mode(is_dark)
        
    def save_settings(self):
        """حفظ الإعدادات"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("dark_mode", self.dark_mode_action.isChecked())
        
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if self.tray_icon and self.tray_icon.isVisible():
            self.hide()
            event.ignore()
        else:
            self.save_settings()
            event.accept()
            
    # أحداث السحب والإفلات
    def dragEnterEvent(self, event: QDragEnterEvent):
        """عند دخول عنصر مسحوب"""
        if event.mimeData().hasUrls() or event.mimeData().hasText():
            event.acceptProposedAction()
            
    def dropEvent(self, event: QDropEvent):
        """عند إفلات عنصر"""
        mime_data = event.mimeData()
        
        if mime_data.hasUrls():
            for url in mime_data.urls():
                url_string = url.toString()
                if url_string.startswith(('http://', 'https://')):
                    self.show_add_download_dialog(url_string)
        elif mime_data.hasText():
            text = mime_data.text()
            if text.startswith(('http://', 'https://')):
                self.show_add_download_dialog(text)
                
        event.acceptProposedAction()