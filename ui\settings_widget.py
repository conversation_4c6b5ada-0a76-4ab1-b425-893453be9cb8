#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الإعدادات
Settings Widget
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLineEdit, QPushButton, QSpinBox, QComboBox, QCheckBox,
    QSlider, QLabel, QFileDialog, QMessageBox, QTabWidget,
    QScrollArea, QFrame, QButtonGroup, QRadioButton, QTextEdit
)
from PySide6.QtCore import Qt, QSettings, Signal
from PySide6.QtGui import QFont

from core.theme_manager import ThemeManager

class SettingsWidget(QWidget):
    """واجهة الإعدادات"""
    
    # الإشارات
    settings_changed = Signal()
    theme_changed = Signal(str)
    language_changed = Signal(str)
    
    def __init__(self, settings: QSettings, theme_manager: ThemeManager):
        super().__init__()
        self.settings = settings
        self.theme_manager = theme_manager
        
        self.setup_ui()
        self.load_settings()
        self.connect_signals()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # الويدجت الرئيسي للإعدادات
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setSpacing(15)
        
        # التبويبات
        self.create_settings_tabs()
        settings_layout.addWidget(self.settings_tabs)
        
        # أزرار الحفظ والإلغاء
        self.create_action_buttons()
        settings_layout.addWidget(self.buttons_frame)
        
        scroll_area.setWidget(settings_widget)
        layout.addWidget(scroll_area)
        
    def create_settings_tabs(self):
        """إنشاء تبويبات الإعدادات"""
        self.settings_tabs = QTabWidget()
        
        # تبويب الإعدادات العامة
        self.create_general_tab()
        self.settings_tabs.addTab(self.general_widget, "عام")
        
        # تبويب إعدادات التحميل
        self.create_download_tab()
        self.settings_tabs.addTab(self.download_widget, "التحميل")
        
        # تبويب إعدادات الشبكة
        self.create_network_tab()
        self.settings_tabs.addTab(self.network_widget, "الشبكة")
        
        # تبويب إعدادات الواجهة
        self.create_interface_tab()
        self.settings_tabs.addTab(self.interface_widget, "الواجهة")
        
        # تبويب الإشعارات
        self.create_notifications_tab()
        self.settings_tabs.addTab(self.notifications_widget, "الإشعارات")
        
        # تبويب الإعدادات المتقدمة
        self.create_advanced_tab()
        self.settings_tabs.addTab(self.advanced_widget, "متقدم")
        
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        self.general_widget = QWidget()
        layout = QVBoxLayout(self.general_widget)
        layout.setSpacing(15)
        
        # مجموعة اللغة والمنطقة
        language_group = QGroupBox("اللغة والمنطقة")
        language_layout = QFormLayout(language_group)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "العربية",
            "English", 
            "Français"
        ])
        language_layout.addRow("اللغة:", self.language_combo)
        
        layout.addWidget(language_group)
        
        # مجموعة مجلدات التحميل
        folders_group = QGroupBox("مجلدات التحميل")
        folders_layout = QFormLayout(folders_group)
        
        # مجلد التحميل الافتراضي
        download_folder_layout = QHBoxLayout()
        self.download_folder_edit = QLineEdit()
        self.download_folder_edit.setText(str(Path.home() / "Downloads"))
        
        self.browse_download_btn = QPushButton("تصفح")
        self.browse_download_btn.setFixedWidth(80)
        self.browse_download_btn.clicked.connect(self.browse_download_folder)
        
        download_folder_layout.addWidget(self.download_folder_edit)
        download_folder_layout.addWidget(self.browse_download_btn)
        
        folders_layout.addRow("مجلد التحميل:", download_folder_layout)
        
        # مجلد الملفات المؤقتة
        temp_folder_layout = QHBoxLayout()
        self.temp_folder_edit = QLineEdit()
        self.temp_folder_edit.setText(str(Path.home() / "Downloads" / "temp"))
        
        self.browse_temp_btn = QPushButton("تصفح")
        self.browse_temp_btn.setFixedWidth(80)
        self.browse_temp_btn.clicked.connect(self.browse_temp_folder)
        
        temp_folder_layout.addWidget(self.temp_folder_edit)
        temp_folder_layout.addWidget(self.browse_temp_btn)
        
        folders_layout.addRow("مجلد مؤقت:", temp_folder_layout)
        
        layout.addWidget(folders_group)
        
        # مجموعة السلوك العام
        behavior_group = QGroupBox("السلوك العام")
        behavior_layout = QFormLayout(behavior_group)
        
        self.start_with_system_check = QCheckBox("تشغيل مع النظام")
        behavior_layout.addRow("", self.start_with_system_check)
        
        self.minimize_to_tray_check = QCheckBox("تصغير إلى شريط النظام")
        behavior_layout.addRow("", self.minimize_to_tray_check)
        
        self.close_to_tray_check = QCheckBox("إغلاق إلى شريط النظام")
        behavior_layout.addRow("", self.close_to_tray_check)
        
        self.auto_detect_clipboard_check = QCheckBox("رصد الحافظة تلقائياً")
        behavior_layout.addRow("", self.auto_detect_clipboard_check)
        
        layout.addWidget(behavior_group)
        layout.addStretch()
        
    def create_download_tab(self):
        """إنشاء تبويب إعدادات التحميل"""
        self.download_widget = QWidget()
        layout = QVBoxLayout(self.download_widget)
        layout.setSpacing(15)
        
        # مجموعة إعدادات التحميل
        download_group = QGroupBox("إعدادات التحميل")
        download_layout = QFormLayout(download_group)
        
        # عدد التحميلات المتوازية
        self.max_concurrent_spin = QSpinBox()
        self.max_concurrent_spin.setRange(1, 10)
        self.max_concurrent_spin.setValue(3)
        download_layout.addRow("التحميلات المتوازية:", self.max_concurrent_spin)
        
        # عدد الاتصالات لكل تحميل
        self.max_connections_spin = QSpinBox()
        self.max_connections_spin.setRange(1, 16)
        self.max_connections_spin.setValue(4)
        download_layout.addRow("الاتصالات لكل تحميل:", self.max_connections_spin)
        
        # حد السرعة
        speed_limit_layout = QHBoxLayout()
        self.speed_limit_check = QCheckBox("تحديد السرعة")
        self.speed_limit_spin = QSpinBox()
        self.speed_limit_spin.setRange(1, 10000)
        self.speed_limit_spin.setValue(1000)
        self.speed_limit_spin.setSuffix(" KB/s")
        self.speed_limit_spin.setEnabled(False)
        
        speed_limit_layout.addWidget(self.speed_limit_check)
        speed_limit_layout.addWidget(self.speed_limit_spin)
        speed_limit_layout.addStretch()
        
        download_layout.addRow("حد السرعة:", speed_limit_layout)
        
        # إعادة المحاولة
        self.retry_count_spin = QSpinBox()
        self.retry_count_spin.setRange(0, 10)
        self.retry_count_spin.setValue(3)
        download_layout.addRow("عدد المحاولات:", self.retry_count_spin)
        
        # مهلة الاتصال
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 300)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" ثانية")
        download_layout.addRow("مهلة الاتصال:", self.timeout_spin)
        
        layout.addWidget(download_group)
        
        # مجموعة السلوك بعد التحميل
        post_download_group = QGroupBox("بعد التحميل")
        post_download_layout = QFormLayout(post_download_group)
        
        self.auto_open_check = QCheckBox("فتح الملف تلقائياً")
        post_download_layout.addRow("", self.auto_open_check)
        
        self.auto_open_folder_check = QCheckBox("فتح مجلد الملف")
        post_download_layout.addRow("", self.auto_open_folder_check)
        
        self.delete_temp_files_check = QCheckBox("حذف الملفات المؤقتة")
        post_download_layout.addRow("", self.delete_temp_files_check)
        
        # صوت الإكمال
        sound_layout = QHBoxLayout()
        self.completion_sound_check = QCheckBox("تشغيل صوت")
        self.sound_file_edit = QLineEdit()
        self.browse_sound_btn = QPushButton("تصفح")
        self.browse_sound_btn.setFixedWidth(80)
        
        sound_layout.addWidget(self.completion_sound_check)
        sound_layout.addWidget(self.sound_file_edit)
        sound_layout.addWidget(self.browse_sound_btn)
        
        post_download_layout.addRow("صوت الإكمال:", sound_layout)
        
        layout.addWidget(post_download_group)
        layout.addStretch()
        
    def create_network_tab(self):
        """إنشاء تبويب إعدادات الشبكة"""
        self.network_widget = QWidget()
        layout = QVBoxLayout(self.network_widget)
        layout.setSpacing(15)
        
        # مجموعة البروكسي
        proxy_group = QGroupBox("إعدادات البروكسي")
        proxy_layout = QFormLayout(proxy_group)
        
        # نوع البروكسي
        self.proxy_type_combo = QComboBox()
        self.proxy_type_combo.addItems([
            "بدون بروكسي",
            "HTTP",
            "HTTPS", 
            "SOCKS4",
            "SOCKS5"
        ])
        proxy_layout.addRow("نوع البروكسي:", self.proxy_type_combo)
        
        # عنوان البروكسي
        self.proxy_host_edit = QLineEdit()
        self.proxy_host_edit.setPlaceholderText("127.0.0.1")
        proxy_layout.addRow("العنوان:", self.proxy_host_edit)
        
        # منفذ البروكسي
        self.proxy_port_spin = QSpinBox()
        self.proxy_port_spin.setRange(1, 65535)
        self.proxy_port_spin.setValue(8080)
        proxy_layout.addRow("المنفذ:", self.proxy_port_spin)
        
        # اسم المستخدم وكلمة المرور
        self.proxy_username_edit = QLineEdit()
        proxy_layout.addRow("اسم المستخدم:", self.proxy_username_edit)
        
        self.proxy_password_edit = QLineEdit()
        self.proxy_password_edit.setEchoMode(QLineEdit.Password)
        proxy_layout.addRow("كلمة المرور:", self.proxy_password_edit)
        
        layout.addWidget(proxy_group)
        
        # مجموعة User Agent
        user_agent_group = QGroupBox("User Agent")
        user_agent_layout = QFormLayout(user_agent_group)
        
        self.user_agent_combo = QComboBox()
        self.user_agent_combo.setEditable(True)
        self.user_agent_combo.addItems([
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        ])
        user_agent_layout.addRow("User Agent:", self.user_agent_combo)
        
        layout.addWidget(user_agent_group)
        
        # مجموعة Headers إضافية
        headers_group = QGroupBox("Headers إضافية")
        headers_layout = QVBoxLayout(headers_group)
        
        self.custom_headers_edit = QTextEdit()
        self.custom_headers_edit.setMaximumHeight(100)
        self.custom_headers_edit.setPlaceholderText(
            "أدخل headers إضافية (كل header في سطر منفصل)\n"
            "مثال: Authorization: Bearer token"
        )
        headers_layout.addWidget(self.custom_headers_edit)
        
        layout.addWidget(headers_group)
        layout.addStretch()
        
    def create_interface_tab(self):
        """إنشاء تبويب إعدادات الواجهة"""
        self.interface_widget = QWidget()
        layout = QVBoxLayout(self.interface_widget)
        layout.setSpacing(15)
        
        # مجموعة الثيم
        theme_group = QGroupBox("المظهر")
        theme_layout = QFormLayout(theme_group)
        
        # اختيار الثيم
        self.theme_combo = QComboBox()
        themes = self.theme_manager.get_available_themes()
        for theme in themes:
            theme_names = {
                "light": "فاتح",
                "dark": "مظلم",
                "blue": "أزرق",
                "green": "أخضر"
            }
            self.theme_combo.addItem(theme_names.get(theme, theme), theme)
        theme_layout.addRow("الثيم:", self.theme_combo)
        
        # الوضع المظلم
        self.dark_mode_check = QCheckBox("الوضع المظلم")
        theme_layout.addRow("", self.dark_mode_check)
        
        layout.addWidget(theme_group)
        
        # مجموعة الخط
        font_group = QGroupBox("الخط")
        font_layout = QFormLayout(font_group)
        
        # حجم الخط
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setValue(9)
        font_layout.addRow("حجم الخط:", self.font_size_spin)
        
        # عائلة الخط
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems([
            "Segoe UI",
            "Arial",
            "Tahoma",
            "Calibri",
            "Times New Roman"
        ])
        font_layout.addRow("نوع الخط:", self.font_family_combo)
        
        layout.addWidget(font_group)
        
        # مجموعة النوافذ
        window_group = QGroupBox("النوافذ")
        window_layout = QFormLayout(window_group)
        
        self.remember_window_size_check = QCheckBox("تذكر حجم النافذة")
        window_layout.addRow("", self.remember_window_size_check)
        
        self.always_on_top_check = QCheckBox("دائماً في المقدمة")
        window_layout.addRow("", self.always_on_top_check)
        
        # شفافية النافذة
        opacity_layout = QHBoxLayout()
        self.window_opacity_slider = QSlider(Qt.Horizontal)
        self.window_opacity_slider.setRange(50, 100)
        self.window_opacity_slider.setValue(100)
        
        self.opacity_label = QLabel("100%")
        self.window_opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )
        
        opacity_layout.addWidget(self.window_opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        
        window_layout.addRow("الشفافية:", opacity_layout)
        
        layout.addWidget(window_group)
        layout.addStretch()
        
    def create_notifications_tab(self):
        """إنشاء تبويب الإشعارات"""
        self.notifications_widget = QWidget()
        layout = QVBoxLayout(self.notifications_widget)
        layout.setSpacing(15)
        
        # مجموعة الإشعارات
        notifications_group = QGroupBox("الإشعارات")
        notifications_layout = QFormLayout(notifications_group)
        
        self.show_notifications_check = QCheckBox("إظهار الإشعارات")
        notifications_layout.addRow("", self.show_notifications_check)
        
        self.notify_on_completion_check = QCheckBox("عند اكتمال التحميل")
        notifications_layout.addRow("", self.notify_on_completion_check)
        
        self.notify_on_failure_check = QCheckBox("عند فشل التحميل")
        notifications_layout.addRow("", self.notify_on_failure_check)
        
        self.notify_on_all_completed_check = QCheckBox("عند اكتمال جميع التحميلات")
        notifications_layout.addRow("", self.notify_on_all_completed_check)
        
        # مدة الإشعار
        self.notification_duration_spin = QSpinBox()
        self.notification_duration_spin.setRange(1, 30)
        self.notification_duration_spin.setValue(5)
        self.notification_duration_spin.setSuffix(" ثانية")
        notifications_layout.addRow("مدة الإشعار:", self.notification_duration_spin)
        
        layout.addWidget(notifications_group)
        
        # مجموعة الأصوات
        sounds_group = QGroupBox("الأصوات")
        sounds_layout = QFormLayout(sounds_group)
        
        self.enable_sounds_check = QCheckBox("تفعيل الأصوات")
        sounds_layout.addRow("", self.enable_sounds_check)
        
        # مستوى الصوت
        volume_layout = QHBoxLayout()
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        
        self.volume_label = QLabel("50%")
        self.volume_slider.valueChanged.connect(
            lambda v: self.volume_label.setText(f"{v}%")
        )
        
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_label)
        
        sounds_layout.addRow("مستوى الصوت:", volume_layout)
        
        layout.addWidget(sounds_group)
        layout.addStretch()
        
    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        self.advanced_widget = QWidget()
        layout = QVBoxLayout(self.advanced_widget)
        layout.setSpacing(15)
        
        # مجموعة قاعدة البيانات
        database_group = QGroupBox("قاعدة البيانات")
        database_layout = QFormLayout(database_group)
        
        # مسار قاعدة البيانات
        db_path_layout = QHBoxLayout()
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setText("downloads.db")
        
        self.browse_db_btn = QPushButton("تصفح")
        self.browse_db_btn.setFixedWidth(80)
        
        db_path_layout.addWidget(self.db_path_edit)
        db_path_layout.addWidget(self.browse_db_btn)
        
        database_layout.addRow("مسار قاعدة البيانات:", db_path_layout)
        
        # تنظيف البيانات القديمة
        self.auto_cleanup_check = QCheckBox("تنظيف تلقائي للبيانات القديمة")
        database_layout.addRow("", self.auto_cleanup_check)
        
        self.cleanup_days_spin = QSpinBox()
        self.cleanup_days_spin.setRange(7, 365)
        self.cleanup_days_spin.setValue(90)
        self.cleanup_days_spin.setSuffix(" يوم")
        database_layout.addRow("حذف البيانات الأقدم من:", self.cleanup_days_spin)
        
        layout.addWidget(database_group)
        
        # مجموعة السجلات
        logging_group = QGroupBox("السجلات")
        logging_layout = QFormLayout(logging_group)
        
        self.enable_logging_check = QCheckBox("تفعيل السجلات")
        logging_layout.addRow("", self.enable_logging_check)
        
        # مستوى السجل
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems([
            "خطأ فقط",
            "تحذير",
            "معلومات",
            "تفصيلي"
        ])
        self.log_level_combo.setCurrentIndex(2)
        logging_layout.addRow("مستوى السجل:", self.log_level_combo)
        
        # مجلد السجلات
        log_folder_layout = QHBoxLayout()
        self.log_folder_edit = QLineEdit()
        self.log_folder_edit.setText("logs")
        
        self.browse_log_btn = QPushButton("تصفح")
        self.browse_log_btn.setFixedWidth(80)
        
        log_folder_layout.addWidget(self.log_folder_edit)
        log_folder_layout.addWidget(self.browse_log_btn)
        
        logging_layout.addRow("مجلد السجلات:", log_folder_layout)
        
        layout.addWidget(logging_group)
        
        # مجموعة الأداء
        performance_group = QGroupBox("الأداء")
        performance_layout = QFormLayout(performance_group)
        
        # حجم المخزن المؤقت
        self.buffer_size_spin = QSpinBox()
        self.buffer_size_spin.setRange(1, 64)
        self.buffer_size_spin.setValue(8)
        self.buffer_size_spin.setSuffix(" KB")
        performance_layout.addRow("حجم المخزن المؤقت:", self.buffer_size_spin)
        
        # تحديث واجهة المستخدم
        self.ui_update_interval_spin = QSpinBox()
        self.ui_update_interval_spin.setRange(100, 5000)
        self.ui_update_interval_spin.setValue(1000)
        self.ui_update_interval_spin.setSuffix(" مللي ثانية")
        performance_layout.addRow("تحديث الواجهة:", self.ui_update_interval_spin)
        
        layout.addWidget(performance_group)
        layout.addStretch()
        
    def create_action_buttons(self):
        """إنشاء أزرار الإجراءات"""
        self.buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(self.buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر استعادة الافتراضي
        self.reset_btn = QPushButton("استعادة الافتراضي")
        self.reset_btn.setFixedSize(120, 35)
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setFixedSize(80, 35)
        self.cancel_btn.clicked.connect(self.load_settings)
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setFixedSize(80, 35)
        self.save_btn.setDefault(True)
        self.save_btn.clicked.connect(self.save_settings)
        
        buttons_layout.addWidget(self.reset_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
    def connect_signals(self):
        """ربط الإشارات"""
        # ربط تغيير الثيم
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        self.dark_mode_check.toggled.connect(self.on_dark_mode_toggled)
        
        # ربط تحديد السرعة
        self.speed_limit_check.toggled.connect(
            self.speed_limit_spin.setEnabled
        )
        
        # ربط تغيير اللغة
        self.language_combo.currentTextChanged.connect(self.on_language_changed)
        
    def browse_download_folder(self):
        """تصفح مجلد التحميل"""
        folder = QFileDialog.getExistingDirectory(
            self, "اختر مجلد التحميل", self.download_folder_edit.text()
        )
        if folder:
            self.download_folder_edit.setText(folder)
            
    def browse_temp_folder(self):
        """تصفح المجلد المؤقت"""
        folder = QFileDialog.getExistingDirectory(
            self, "اختر المجلد المؤقت", self.temp_folder_edit.text()
        )
        if folder:
            self.temp_folder_edit.setText(folder)
            
    def on_theme_changed(self):
        """عند تغيير الثيم"""
        theme_data = self.theme_combo.currentData()
        if theme_data:
            self.theme_manager.set_theme(theme_data)
            self.theme_changed.emit(theme_data)
            
    def on_dark_mode_toggled(self, checked: bool):
        """عند تبديل الوضع المظلم"""
        self.theme_manager.set_dark_mode(checked)
        if checked:
            self.theme_combo.setCurrentText("مظلم")
        else:
            self.theme_combo.setCurrentText("فاتح")
            
    def on_language_changed(self):
        """عند تغيير اللغة"""
        language = self.language_combo.currentText()
        language_codes = {
            "العربية": "ar",
            "English": "en",
            "Français": "fr"
        }
        
        code = language_codes.get(language, "ar")
        self.language_changed.emit(code)
        
    def load_settings(self):
        """تحميل الإعدادات"""
        # الإعدادات العامة
        self.language_combo.setCurrentText(
            self.settings.value("language", "العربية")
        )
        self.download_folder_edit.setText(
            self.settings.value("download_folder", str(Path.home() / "Downloads"))
        )
        self.temp_folder_edit.setText(
            self.settings.value("temp_folder", str(Path.home() / "Downloads" / "temp"))
        )
        
        # السلوك العام
        self.start_with_system_check.setChecked(
            self.settings.value("start_with_system", False, type=bool)
        )
        self.minimize_to_tray_check.setChecked(
            self.settings.value("minimize_to_tray", True, type=bool)
        )
        self.close_to_tray_check.setChecked(
            self.settings.value("close_to_tray", True, type=bool)
        )
        self.auto_detect_clipboard_check.setChecked(
            self.settings.value("auto_detect_clipboard", True, type=bool)
        )
        
        # إعدادات التحميل
        self.max_concurrent_spin.setValue(
            self.settings.value("max_concurrent_downloads", 3, type=int)
        )
        self.max_connections_spin.setValue(
            self.settings.value("max_connections_per_download", 4, type=int)
        )
        self.speed_limit_check.setChecked(
            self.settings.value("enable_speed_limit", False, type=bool)
        )
        self.speed_limit_spin.setValue(
            self.settings.value("speed_limit", 1000, type=int)
        )
        self.retry_count_spin.setValue(
            self.settings.value("retry_count", 3, type=int)
        )
        self.timeout_spin.setValue(
            self.settings.value("connection_timeout", 30, type=int)
        )
        
        # السلوك بعد التحميل
        self.auto_open_check.setChecked(
            self.settings.value("auto_open_file", False, type=bool)
        )
        self.auto_open_folder_check.setChecked(
            self.settings.value("auto_open_folder", False, type=bool)
        )
        self.delete_temp_files_check.setChecked(
            self.settings.value("delete_temp_files", True, type=bool)
        )
        self.completion_sound_check.setChecked(
            self.settings.value("play_completion_sound", True, type=bool)
        )
        
        # إعدادات الشبكة
        self.proxy_type_combo.setCurrentText(
            self.settings.value("proxy_type", "بدون بروكسي")
        )
        self.proxy_host_edit.setText(
            self.settings.value("proxy_host", "")
        )
        self.proxy_port_spin.setValue(
            self.settings.value("proxy_port", 8080, type=int)
        )
        self.proxy_username_edit.setText(
            self.settings.value("proxy_username", "")
        )
        self.proxy_password_edit.setText(
            self.settings.value("proxy_password", "")
        )
        
        # إعدادات الواجهة
        current_theme = self.theme_manager.current_theme
        theme_names = {
            "light": "فاتح",
            "dark": "مظلم", 
            "blue": "أزرق",
            "green": "أخضر"
        }
        self.theme_combo.setCurrentText(theme_names.get(current_theme, "فاتح"))
        self.dark_mode_check.setChecked(self.theme_manager.is_dark_mode)
        
        self.font_size_spin.setValue(
            self.settings.value("font_size", 9, type=int)
        )
        self.font_family_combo.setCurrentText(
            self.settings.value("font_family", "Segoe UI")
        )
        
        # الإشعارات
        self.show_notifications_check.setChecked(
            self.settings.value("show_notifications", True, type=bool)
        )
        self.notify_on_completion_check.setChecked(
            self.settings.value("notify_on_completion", True, type=bool)
        )
        self.notify_on_failure_check.setChecked(
            self.settings.value("notify_on_failure", True, type=bool)
        )
        
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # الإعدادات العامة
            self.settings.setValue("language", self.language_combo.currentText())
            self.settings.setValue("download_folder", self.download_folder_edit.text())
            self.settings.setValue("temp_folder", self.temp_folder_edit.text())
            
            # السلوك العام
            self.settings.setValue("start_with_system", self.start_with_system_check.isChecked())
            self.settings.setValue("minimize_to_tray", self.minimize_to_tray_check.isChecked())
            self.settings.setValue("close_to_tray", self.close_to_tray_check.isChecked())
            self.settings.setValue("auto_detect_clipboard", self.auto_detect_clipboard_check.isChecked())
            
            # إعدادات التحميل
            self.settings.setValue("max_concurrent_downloads", self.max_concurrent_spin.value())
            self.settings.setValue("max_connections_per_download", self.max_connections_spin.value())
            self.settings.setValue("enable_speed_limit", self.speed_limit_check.isChecked())
            self.settings.setValue("speed_limit", self.speed_limit_spin.value())
            self.settings.setValue("retry_count", self.retry_count_spin.value())
            self.settings.setValue("connection_timeout", self.timeout_spin.value())
            
            # السلوك بعد التحميل
            self.settings.setValue("auto_open_file", self.auto_open_check.isChecked())
            self.settings.setValue("auto_open_folder", self.auto_open_folder_check.isChecked())
            self.settings.setValue("delete_temp_files", self.delete_temp_files_check.isChecked())
            self.settings.setValue("play_completion_sound", self.completion_sound_check.isChecked())
            
            # إعدادات الشبكة
            self.settings.setValue("proxy_type", self.proxy_type_combo.currentText())
            self.settings.setValue("proxy_host", self.proxy_host_edit.text())
            self.settings.setValue("proxy_port", self.proxy_port_spin.value())
            self.settings.setValue("proxy_username", self.proxy_username_edit.text())
            self.settings.setValue("proxy_password", self.proxy_password_edit.text())
            
            # إعدادات الواجهة
            self.settings.setValue("font_size", self.font_size_spin.value())
            self.settings.setValue("font_family", self.font_family_combo.currentText())
            
            # الإشعارات
            self.settings.setValue("show_notifications", self.show_notifications_check.isChecked())
            self.settings.setValue("notify_on_completion", self.notify_on_completion_check.isChecked())
            self.settings.setValue("notify_on_failure", self.notify_on_failure_check.isChecked())
            
            # إرسال إشارة التغيير
            self.settings_changed.emit()
            
            QMessageBox.information(self, "تم", "تم حفظ الإعدادات بنجاح")
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
            
    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # مسح جميع الإعدادات
            self.settings.clear()
            
            # إعادة تحميل الإعدادات الافتراضية
            self.load_settings()
            
            QMessageBox.information(self, "تم", "تم استعادة الإعدادات الافتراضية")